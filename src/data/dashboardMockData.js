// Mock data for the dashboard

// Customer data
export const customerData = {
  total: 1245,
  topCustomers: [
    { id: 1, name: '<PERSON>', orders: 32, spent: 4850 },
    { id: 2, name: '<PERSON>', orders: 28, spent: 3920 },
    { id: 3, name: '<PERSON>', orders: 25, spent: 3750 }
  ]
};

// Vendor data
export const vendorData = {
  total: 87,
  approved: 65,
  pending: 22,
  topVendors: [
    { id: 1, name: 'Tech Solutions LLC', products: 120, sales: 28500 },
    { id: 2, name: 'Fashion Hub', products: 95, sales: 22400 },
    { id: 3, name: 'Home Essentials', products: 78, sales: 18900 }
  ]
};

// Product data
export const productData = {
  total: 1876,
  inHouse: 876,
  vendor: 1000,
  topProducts: [
    { id: 1, name: 'Wireless Earbuds', sales: 352, rating: 4.8, vendor: 'Tech Solutions LLC' },
    { id: 2, name: 'Smart Watch', sales: 287, rating: 4.7, vendor: 'Tech Solutions LLC' },
    { id: 3, name: 'Leather Handbag', sales: 245, rating: 4.9, vendor: 'Fashion Hub' }
  ]
};

// Promotion data
export const promotionData = {
  total: 24,
  productsOnPromotion: 156,
  sold: 98
};

// Category data
export const categoryData = {
  total: 32,
  topCategories: [
    { id: 1, name: 'Electronics', sales: 45600, products: 320, source: 'in-house' },
    { id: 2, name: 'Fashion', sales: 38900, products: 450, source: 'vendor' },
    { id: 3, name: 'Home & Kitchen', sales: 32400, products: 280, source: 'in-house' }
  ],
  // New data structure for the redesigned dashboard
  '1d': [
    { name: 'VHMS', inHouseSales: 12500, vendorSales: 8200, inHouseUnits: 85, vendorUnits: 55 },
    { name: 'Beauty', inHouseSales: 9800, vendorSales: 15600, inHouseUnits: 120, vendorUnits: 180 },
    { name: 'Food & Drink', inHouseSales: 8900, vendorSales: 6500, inHouseUnits: 75, vendorUnits: 60 },
    { name: 'Sports Nutrition', inHouseSales: 7200, vendorSales: 5800, inHouseUnits: 65, vendorUnits: 50 },
    { name: 'Weight Management', inHouseSales: 6500, vendorSales: 4900, inHouseUnits: 55, vendorUnits: 45 },
    { name: 'Para Pharmacy', inHouseSales: 7500, vendorSales: 5900, inHouseUnits: 50, vendorUnits: 35 },
    { name: 'Med Equipment', inHouseSales: 8500, vendorSales: 5400, inHouseUnits: 70, vendorUnits: 45 },
    { name: 'Misc Gen Items', inHouseSales: 9200, vendorSales: 6400, inHouseUnits: 60, vendorUnits: 50 }
  ],
  '7d': [
    { name: 'VHMS', inHouseSales: 28500, vendorSales: 18900, inHouseUnits: 195, vendorUnits: 125 },
    { name: 'Beauty', inHouseSales: 22400, vendorSales: 32800, inHouseUnits: 280, vendorUnits: 390 },
    { name: 'Food & Drink', inHouseSales: 19800, vendorSales: 14500, inHouseUnits: 165, vendorUnits: 130 },
    { name: 'Sports Nutrition', inHouseSales: 16500, vendorSales: 12800, inHouseUnits: 145, vendorUnits: 110 },
    { name: 'Weight Management', inHouseSales: 14200, vendorSales: 10900, inHouseUnits: 125, vendorUnits: 95 },
    { name: 'Para Pharmacy', inHouseSales: 17500, vendorSales: 13200, inHouseUnits: 155, vendorUnits: 105 },
    { name: 'Med Equipment', inHouseSales: 19200, vendorSales: 14800, inHouseUnits: 170, vendorUnits: 120 },
    { name: 'Misc Gen Items', inHouseSales: 21500, vendorSales: 16800, inHouseUnits: 190, vendorUnits: 140 }
  ],
  '30d': [
    { name: 'VHMS', inHouseSales: 45600, vendorSales: 32400, inHouseUnits: 320, vendorUnits: 210 },
    { name: 'Beauty', inHouseSales: 38900, vendorSales: 56700, inHouseUnits: 450, vendorUnits: 620 },
    { name: 'Food & Drink', inHouseSales: 32400, vendorSales: 24800, inHouseUnits: 280, vendorUnits: 220 },
    { name: 'Sports Nutrition', inHouseSales: 28700, vendorSales: 21500, inHouseUnits: 240, vendorUnits: 190 },
    { name: 'Weight Management', inHouseSales: 24500, vendorSales: 18700, inHouseUnits: 210, vendorUnits: 160 },
    { name: 'Para Pharmacy', inHouseSales: 27800, vendorSales: 21200, inHouseUnits: 245, vendorUnits: 180 },
    { name: 'Med Equipment', inHouseSales: 31200, vendorSales: 24500, inHouseUnits: 280, vendorUnits: 210 },
    { name: 'Misc Gen Items', inHouseSales: 34500, vendorSales: 27800, inHouseUnits: 310, vendorUnits: 240 }
  ]
};

// Brand data
export const brandData = {
  total: 64,
  topBrands: [
    { id: 1, name: 'Apple', sales: 58700, products: 45 },
    { id: 2, name: 'Samsung', sales: 42300, products: 62 },
    { id: 3, name: 'Nike', sales: 36800, products: 78 },
    { id: 4, name: 'Adidas', sales: 35200, products: 56 },
    { id: 5, name: 'Sony', sales: 31500, products: 48 },
    { id: 6, name: 'LG', sales: 30200, products: 52 },
    { id: 7, name: 'HP', sales: 28500, products: 40 },
    { id: 8, name: 'Dell', sales: 27800, products: 38 },
    { id: 9, name: 'Lenovo', sales: 26500, products: 35 },
    { id: 10, name: 'Asus', sales: 25800, products: 32 }
  ],
  // New data structure for the redesigned dashboard
  '1d': [
    { name: 'Apple', sales: 15800, units: 95 },
    { name: 'Samsung', sales: 12500, units: 85 },
    { name: 'Nike', sales: 10200, units: 120 },
    { name: 'Adidas', sales: 9800, units: 110 },
    { name: 'Sony', sales: 8500, units: 75 },
    { name: 'LG', sales: 7900, units: 65 },
    { name: 'HP', sales: 7200, units: 60 },
    { name: 'Dell', sales: 6800, units: 55 },
    { name: 'Lenovo', sales: 6500, units: 50 },
    { name: 'Asus', sales: 6200, units: 45 },
  ],
  '7d': [
    { name: 'Apple', sales: 35600, units: 215 },
    { name: 'Samsung', sales: 28200, units: 190 },
    { name: 'Nike', sales: 22900, units: 270 },
    { name: 'Adidas', sales: 22100, units: 250 },
    { name: 'Sony', sales: 19200, units: 170 },
    { name: 'LG', sales: 17800, units: 145 },
    { name: 'HP', sales: 16200, units: 135 },
    { name: 'Dell', sales: 15300, units: 125 },
    { name: 'Lenovo', sales: 14600, units: 115 },
    { name: 'Asus', sales: 14000, units: 105 },
  ],
  '30d': [
    { name: 'Apple', sales: 58700, units: 350 },
    { name: 'Samsung', sales: 42300, units: 310 },
    { name: 'Nike', sales: 36800, units: 440 },
    { name: 'Adidas', sales: 35200, units: 410 },
    { name: 'Sony', sales: 31500, units: 280 },
    { name: 'LG', sales: 29200, units: 240 },
    { name: 'HP', sales: 26800, units: 220 },
    { name: 'Dell', sales: 25400, units: 205 },
    { name: 'Lenovo', sales: 24100, units: 190 },
    { name: 'Asus', sales: 22800, units: 175 },
  ]
};

// Revenue data
export const revenueData = {
  total: 248750,
  fromVendor: 142300,
  platformFees: 28500,
  shippingRevenue: 18200,
  advertisingRevenue: 12800
};

// Order data
export const orderData = {
  total: 876,
  placed: 125,
  confirmed: 320,
  processed: 380,
  pending: 51
};

// Task priority data
export const taskPriorityData = [
  {
    id: 1,
    title: 'Approve product promotions',
    priority: 'high',
    icon: 'promotion',
    progress: { current: 15, total: 24 },
    completed: false
  },
  {
    id: 2,
    title: 'Respond to customer inquiries',
    priority: 'high',
    icon: 'customer',
    progress: { current: 10, total: 25 },
    completed: false
  },
  {
    id: 3,
    title: 'Process vendor applications',
    priority: 'medium',
    icon: 'vendor',
    progress: { current: 1, total: 15 },
    completed: false
  },
  {
    id: 4,
    title: 'Review new product listings',
    priority: 'medium',
    icon: 'product',
    progress: { current: 1, total: 50 },
    completed: false
  },
  {
    id: 5,
    title: 'Respond to advertisement requests',
    priority: 'medium',
    icon: 'promotion',
    progress: { current: 1, total: 10 },
    completed: false
  },
  {
    id: 6,
    title: 'Create new product categories',
    priority: 'low',
    icon: 'category',
    progress: { current: 5, total: 5 },
    completed: true
  },
  {
    id: 7,
    title: 'Resolve support tickets',
    priority: 'high',
    icon: 'task',
    progress: { current: 12, total: 12 },
    completed: true
  }
];

// Order status data
export const orderStatusData = [
  { id: 10045, customer: 'John Doe', amount: 245.99, status: 'cancelled' },
  { id: 10044, customer: 'Sarah Williams', amount: 189.50, status: 'pending' },
  { id: 10043, customer: 'Michael Brown', amount: 320.75, status: 'processing' },
  { id: 10042, customer: 'Emily Davis', amount: 175.25, status: 'confirmed' },
  { id: 10041, customer: 'David Wilson', amount: 430.00, status: 'completed' }
];

// Sales chart data
export const salesChartData = {
  daily: [
    { name: '12 AM', sales: 1200, orders: 8 },
    { name: '4 AM', sales: 800, orders: 5 },
    { name: '8 AM', sales: 2500, orders: 15 },
    { name: '12 PM', sales: 3800, orders: 22 },
    { name: '4 PM', sales: 4200, orders: 25 },
    { name: '8 PM', sales: 3500, orders: 20 },
    { name: '11 PM', sales: 2000, orders: 12 }
  ],
  weekly: [
    { name: 'Mon', sales: 12500, orders: 75 },
    { name: 'Tue', sales: 14800, orders: 85 },
    { name: 'Wed', sales: 13200, orders: 78 },
    { name: 'Thu', sales: 15600, orders: 92 },
    { name: 'Fri', sales: 18900, orders: 110 },
    { name: 'Sat', sales: 21500, orders: 125 },
    { name: 'Sun', sales: 19200, orders: 115 }
  ],
  monthly: [
    { name: 'Jan', sales: 85000, orders: 520 },
    { name: 'Feb', sales: 78000, orders: 480 },
    { name: 'Mar', sales: 92000, orders: 560 },
    { name: 'Apr', sales: 105000, orders: 620 },
    { name: 'May', sales: 118000, orders: 680 },
    { name: 'Jun', sales: 125000, orders: 720 },
    { name: 'Jul', sales: 132000, orders: 760 }
  ]
};

// New data for the redesigned dashboard
export const saleMetrics = {
  '1d': [
    { label: 'Total Sales (AED Value)', value: '45,800' },
    { label: 'Total Transactions', value: '325' },
    { label: 'Total Product Units Sold', value: '520' },
    { label: 'Total Units Returned', value: '12' },
    { label: 'Total Refund Value', value: '2,450' },
    { label: 'TOTAL NET SALE', value: '43,350' }
  ],
  '7d': [
    { label: 'Total Sales (AED Value)', value: '285,600' },
    { label: 'Total Transactions', value: '1,890' },
    { label: 'Total Product Units Sold', value: '3,250' },
    { label: 'Total Units Returned', value: '85' },
    { label: 'Total Refund Value', value: '15,800' },
    { label: 'TOTAL NET SALE', value: '269,800' }
  ],
  '30d': [
    { label: 'Total Sales (AED Value)', value: '1,245,000' },
    { label: 'Total Transactions', value: '8,750' },
    { label: 'Total Product Units Sold', value: '14,500' },
    { label: 'Total Units Returned', value: '320' },
    { label: 'Total Refund Value', value: '68,500' },
    { label: 'TOTAL NET SALE', value: '1,176,500' }
  ]
};

export const orderMetrics = {
  '1d': [
    { label: 'Total Orders', value: '325' },
    { label: 'Delivered', value: '180' },
    { label: 'Pending', value: '120' },
    { label: 'Cancelled', value: '15' },
    { label: 'Returns', value: '10' },
    { label: 'ESTIMATED SHIPPING COST', value: '4,850' }
  ],
  '7d': [
    { label: 'Total Orders', value: '1,890' },
    { label: 'Delivered', value: '1,250' },
    { label: 'Pending', value: '520' },
    { label: 'Cancelled', value: '85' },
    { label: 'Returns', value: '35' },
    { label: 'ESTIMATED SHIPPING COST', value: '28,350' }
  ],
  '30d': [
    { label: 'Total Orders', value: '8,750' },
    { label: 'Delivered', value: '6,850' },
    { label: 'Pending', value: '1,450' },
    { label: 'Cancelled', value: '320' },
    { label: 'Returns', value: '130' },
    { label: 'ESTIMATED SHIPPING COST', value: '131,250' }
  ]
};

export const supportMetrics = {
  '1d': [
    { label: 'Total Tickets Generated', value: '45' },
    { label: 'By Customers', value: '32' },
    { label: 'By Vendors', value: '10' },
    { label: 'By Trade Partners', value: '3' },
    { label: 'Total Resolved', value: '28' },
    { label: 'SUPPORT EFFICIENCY', value: '62%' }
  ],
  '7d': [
    { label: 'Total Tickets Generated', value: '285' },
    { label: 'By Customers', value: '195' },
    { label: 'By Vendors', value: '75' },
    { label: 'By Trade Partners', value: '15' },
    { label: 'Total Resolved', value: '210' },
    { label: 'SUPPORT EFFICIENCY', value: '74%' }
  ],
  '30d': [
    { label: 'Total Tickets Generated', value: '1,250' },
    { label: 'By Customers', value: '850' },
    { label: 'By Vendors', value: '320' },
    { label: 'By Trade Partners', value: '80' },
    { label: 'Total Resolved', value: '1,050' },
    { label: 'SUPPORT EFFICIENCY', value: '84%' }
  ]
};

export const promotionMetrics = {
  '1d': [
    { label: 'Total Promos Running', value: '156' },
    { label: 'Vendors Running Promo', value: '18' },
    { label: 'Products on Promo', value: '156' },
    { label: 'Units SOLD on Promo', value: '85' },
    { label: 'Promo Sales Value', value: '12,500' },
    { label: 'Promo ROI', value: '185%' },
    { label: 'PROMO SALES RATIO', value: '28%' }
  ],
  '7d': [
    { label: 'Total Promos Running', value: '210' },
    { label: 'Vendors Running Promo', value: '24' },
    { label: 'Products on Promo', value: '210' },
    { label: 'Units SOLD on Promo', value: '520' },
    { label: 'Promo Sales Value', value: '78,500' },
    { label: 'Promo ROI', value: '210%' },
    { label: 'PROMO SALES RATIO', value: '32%' }
  ],
  '30d': [
    { label: 'Total Promos Running', value: '350' },
    { label: 'Vendors Running Promo', value: '32' },
    { label: 'Products on Promo', value: '350' },
    { label: 'Units SOLD on Promo', value: '2,450' },
    { label: 'Promo Sales Value', value: '365,000' },
    { label: 'Promo ROI', value: '245%' },
    { label: 'PROMO SALES RATIO', value: '35%' }
  ]
};

export const vendorMetrics = {
  '1d': [
    { label: 'Total Vendors', value: '87' },
    { label: 'Approved Vendors', value: '65' },
    { label: 'Pending Vendors', value: '22' },
    { label: 'EOI in Last 30 days', value: '15' },
    { label: 'Active Vendor Logins', value: '42' },
    { label: 'VENDOR MANAGEMENT SCORE', value: '78%' }
  ],
  '7d': [
    { label: 'Total Vendors', value: '87' },
    { label: 'Approved Vendors', value: '65' },
    { label: 'Pending Vendors', value: '22' },
    { label: 'EOI in Last 30 days', value: '15' },
    { label: 'Active Vendor Logins', value: '58' },
    { label: 'VENDOR MANAGEMENT SCORE', value: '82%' }
  ],
  '30d': [
    { label: 'Total Vendors', value: '87' },
    { label: 'Approved Vendors', value: '65' },
    { label: 'Pending Vendors', value: '22' },
    { label: 'EOI in Last 30 days', value: '15' },
    { label: 'Active Vendor Logins', value: '72' },
    { label: 'VENDOR MANAGEMENT SCORE', value: '85%' }
  ]
};

export const productMetrics = {
  '1d': [
    { label: 'Total Listed Products', value: '1,876' },
    { label: 'In House Products Listed', value: '876' },
    { label: 'Vendor Listed Products', value: '1,000' },
    { label: 'In House Products SOLD', value: '85' },
    { label: 'Vendor Products SOLD', value: '120' },
    { label: 'RATIO OF SOLD PRODUCTS', value: '10.9%' }
  ],
  '7d': [
    { label: 'Total Listed Products', value: '1,876' },
    { label: 'In House Products Listed', value: '876' },
    { label: 'Vendor Listed Products', value: '1,000' },
    { label: 'In House Products SOLD', value: '520' },
    { label: 'Vendor Products SOLD', value: '750' },
    { label: 'RATIO OF SOLD PRODUCTS', value: '67.7%' }
  ],
  '30d': [
    { label: 'Total Listed Products', value: '1,876' },
    { label: 'In House Products Listed', value: '876' },
    { label: 'Vendor Listed Products', value: '1,000' },
    { label: 'In House Products SOLD', value: '2,250' },
    { label: 'Vendor Products SOLD', value: '3,450' },
    { label: 'RATIO OF SOLD PRODUCTS', value: '304.4%' }
  ]
};

export const topVendors = {
  '1d': [
    { name: 'Tech Solutions LLC', value: '8,500', ratio: '18.6%' },
    { name: 'Fashion Hub', value: '7,200', ratio: '15.7%' },
    { name: 'Home Essentials', value: '6,500', ratio: '14.2%' },
    { name: 'Beauty World', value: '5,800', ratio: '12.7%' },
    { name: 'Sports Center', value: '4,900', ratio: '10.7%' }
  ],
  '7d': [
    { name: 'Tech Solutions LLC', value: '52,500', ratio: '18.4%' },
    { name: 'Fashion Hub', value: '45,800', ratio: '16.0%' },
    { name: 'Home Essentials', value: '38,900', ratio: '13.6%' },
    { name: 'Beauty World', value: '35,600', ratio: '12.5%' },
    { name: 'Sports Center', value: '32,400', ratio: '11.3%' }
  ],
  '30d': [
    { name: 'Tech Solutions LLC', value: '228,500', ratio: '18.4%' },
    { name: 'Fashion Hub', value: '195,600', ratio: '15.7%' },
    { name: 'Home Essentials', value: '168,900', ratio: '13.6%' },
    { name: 'Beauty World', value: '152,300', ratio: '12.2%' },
    { name: 'Sports Center', value: '142,500', ratio: '11.4%' }
  ]
};

export const topProducts = {
  '1d': [
    { name: 'Wireless Earbuds', value: '3,500', ratio: '7.6%' },
    { name: 'Smart Watch', value: '2,800', ratio: '6.1%' },
    { name: 'Leather Handbag', value: '2,450', ratio: '5.3%' },
    { name: 'Fitness Tracker', value: '2,100', ratio: '4.6%' },
    { name: 'Bluetooth Speaker', value: '1,950', ratio: '4.3%' }
  ],
  '7d': [
    { name: 'Wireless Earbuds', value: '21,500', ratio: '7.5%' },
    { name: 'Smart Watch', value: '18,700', ratio: '6.5%' },
    { name: 'Leather Handbag', value: '15,900', ratio: '5.6%' },
    { name: 'Fitness Tracker', value: '14,200', ratio: '5.0%' },
    { name: 'Bluetooth Speaker', value: '12,800', ratio: '4.5%' }
  ],
  '30d': [
    { name: 'Wireless Earbuds', value: '95,200', ratio: '7.6%' },
    { name: 'Smart Watch', value: '82,500', ratio: '6.6%' },
    { name: 'Leather Handbag', value: '72,800', ratio: '5.8%' },
    { name: 'Fitness Tracker', value: '65,400', ratio: '5.2%' },
    { name: 'Bluetooth Speaker', value: '58,900', ratio: '4.7%' }
  ]
};

export const customerMetrics = {
  '1d': [
    { label: 'Total Customers', value: '1,245' },
    { label: 'New Acquired in Last 30 Days', value: '125' },
    { label: 'Visitors Verified log in', value: '320' },
    { label: 'Visitors as Guest', value: '480' },
    { label: 'Buyers', value: '185' },
    { label: 'CONVERSION RATE', value: '23.1%' }
  ],
  '7d': [
    { label: 'Total Customers', value: '1,245' },
    { label: 'New Acquired in Last 30 Days', value: '125' },
    { label: 'Visitors Verified log in', value: '1,850' },
    { label: 'Visitors as Guest', value: '2,950' },
    { label: 'Buyers', value: '1,120' },
    { label: 'CONVERSION RATE', value: '23.3%' }
  ],
  '30d': [
    { label: 'Total Customers', value: '1,245' },
    { label: 'New Acquired in Last 30 Days', value: '125' },
    { label: 'Visitors Verified log in', value: '8,250' },
    { label: 'Visitors as Guest', value: '12,500' },
    { label: 'Buyers', value: '4,850' },
    { label: 'CONVERSION RATE', value: '23.4%' }
  ]
};

export const fulfillmentMetrics = {
  '1d': [
    { label: 'Total', value: '355' },
    { label: 'FBA', value: '85' },
    { label: 'FBP', value: '120' },
    { label: 'Easy Ship', value: '45' },
    { label: 'Direct Ship', value: '65' },
    { label: 'Drop Ship', value: '10' },
    { label: 'ESTIMATED SHIPPING COST', value: '4,850' }
  ],
  '7d': [
    { label: 'Total', value: '1230' },
    { label: 'FBA', value: '520' },
    { label: 'FBP', value: '750' },
    { label: 'Easy Ship', value: '280' },
    { label: 'Direct Ship', value: '320' },
    { label: 'Drop Ship', value: '20' },
    { label: 'ESTIMATED SHIPPING COST', value: '28,350' }
  ],
  '30d': [
    { label: 'Total', value: '8460' },
    { label: 'FBA', value: '2,250' },
    { label: 'FBP', value: '3,450' },
    { label: 'Easy Ship', value: '1,250' },
    { label: 'Direct Ship', value: '1,650' },
    { label: 'Drop Ship', value: '150' },
    { label: 'ESTIMATED SHIPPING COST', value: '131,250' }
  ]
};

export const revenueMetrics = {
  '1d': [
    { label: 'Total', value: '18,500' },
    { label: 'Shipping Fee From Customer', value: '2,850' },
    { label: 'Estimated Commission Earned', value: '5,250' },
    { label: 'Storage & TPL Fee from Vendor', value: '1,850' },
    { label: 'Advertisement Sales Revenue', value: '3,250' },
    { label: 'Pay Per Click Revenue', value: '1,450' },
    { label: 'REVENUE CASH IN', value: '14,650' }
  ],
  '7d': [
    { label: 'Total', value: '94,500' },
    { label: 'Shipping Fee From Customer', value: '18,500' },
    { label: 'Estimated Commission Earned', value: '32,500' },
    { label: 'Storage & TPL Fee from Vendor', value: '12,500' },
    { label: 'Advertisement Sales Revenue', value: '21,500' },
    { label: 'Pay Per Click Revenue', value: '9,500' },
    { label: 'REVENUE CASH IN', value: '94,500' }
  ],
  '30d': [
    { label: 'Total', value: '424,100' },
    { label: 'Shipping Fee From Customer', value: '85,000' },
    { label: 'Estimated Commission Earned', value: '142,300' },
    { label: 'Storage & TPL Fee from Vendor', value: '58,500' },
    { label: 'Advertisement Sales Revenue', value: '95,800' },
    { label: 'Pay Per Click Revenue', value: '42,500' },
    { label: 'REVENUE CASH IN', value: '424,100' }
  ]
};

export const payablesMetrics = {
  '1d': [
    { label: 'Total', value: '38,700' },
    { label: 'Vendor Pay Outs', value: '28,500' },
    { label: 'Fulfillment Storage Pay Out', value: '3,250' },
    { label: 'Shipping Delivery Pay Out', value: '4,850' },
    { label: 'Packaging & Supplies cost', value: '1,250' },
    { label: 'Misc Pay Outs', value: '850' },
    { label: 'ESTIMATED TOTAL PAY OUT', value: '38,700' }
  ],
  '7d': [
    { label: 'Total', value: '239,350' },
    { label: 'Vendor Pay Outs', value: '175,800' },
    { label: 'Fulfillment Storage Pay Out', value: '21,500' },
    { label: 'Shipping Delivery Pay Out', value: '28,350' },
    { label: 'Packaging & Supplies cost', value: '8,500' },
    { label: 'Misc Pay Outs', value: '5,200' },
    { label: 'ESTIMATED TOTAL PAY OUT', value: '239,350' }
  ],
  '30d': [
    { label: 'Total', value: '1,053,050' },
    { label: 'Vendor Pay Outs', value: '765,000' },
    { label: 'Fulfillment Storage Pay Out', value: '95,800' },
    { label: 'Shipping Delivery Pay Out', value: '131,250' },
    { label: 'Packaging & Supplies cost', value: '38,500' },
    { label: 'Misc Pay Outs', value: '22,500' },
    { label: 'ESTIMATED TOTAL PAY OUT', value: '1,053,050' }
  ]
};
