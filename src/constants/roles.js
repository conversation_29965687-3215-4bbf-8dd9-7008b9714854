import * as permissions from './permissions';

/**
 * Role definitions for the application
 * Each role has a set of permissions that determine what actions users with that role can perform
 */

// Super Admin role has all permissions
export const SUPER_ADMIN = 'super_admin';

// Admin role has most permissions except for some sensitive ones
export const ADMIN = 'admin';

// Manager role has permissions to manage day-to-day operations
export const MANAGER = 'manager';

// Editor role has permissions to manage content
export const EDITOR = 'editor';

// Customer Support role has permissions to handle customer inquiries
export const CUSTOMER_SUPPORT = 'customer_support';

// Viewer role has read-only permissions
export const VIEWER = 'viewer';

/**
 * Role permissions mapping
 * Maps each role to its set of permissions
 */
export const rolePermissions = {
  [SUPER_ADMIN]: [
    // Super admin has all permissions
    ...Object.values(permissions),
  ],
  
  [ADMIN]: [
    // Order permissions
    permissions.BROWSE_ORDERS,
    permissions.VIEW_ORDER,
    permissions.CREATE_ORDER,
    permissions.EDIT_ORDER,
    permissions.DELETE_ORDER,
    permissions.DELIVER_ORDER,
    permissions.MANAGE_SHIPMENT_TRACKING,
    
    // Product permissions
    permissions.BROWSE_PRODUCTS,
    permissions.VIEW_PRODUCT,
    permissions.CREATE_PRODUCT,
    permissions.EDIT_PRODUCT,
    permissions.DELETE_PRODUCT,
    permissions.MANAGE_PRODUCT_VARIANTS,
    permissions.MANAGE_SPONSORED_PRODUCTS,
    
    // User permissions
    permissions.BROWSE_USERS,
    permissions.VIEW_USER,
    permissions.CREATE_USER,
    permissions.EDIT_USER,
    permissions.DELETE_USER,
    
    // Vendor permissions
    permissions.BROWSE_VENDORS,
    permissions.VIEW_VENDOR,
    permissions.CREATE_VENDOR,
    permissions.EDIT_VENDOR,
    permissions.DELETE_VENDOR,
    permissions.APPROVE_VENDOR,
    
    // Category permissions
    permissions.BROWSE_CATEGORIES,
    permissions.VIEW_CATEGORY,
    permissions.CREATE_CATEGORY,
    permissions.EDIT_CATEGORY,
    permissions.DELETE_CATEGORY,
    
    // Shipment permissions
    permissions.BROWSE_SHIPMENTS,
    permissions.VIEW_SHIPMENT,
    permissions.CREATE_SHIPMENT,
    permissions.EDIT_SHIPMENT,
    permissions.DELETE_SHIPMENT,
    
    // Coupon permissions
    permissions.BROWSE_COUPONS,
    permissions.VIEW_COUPON,
    permissions.CREATE_COUPON,
    permissions.EDIT_COUPON,
    permissions.DELETE_COUPON,
    
    // Banner permissions
    permissions.BROWSE_BANNERS,
    permissions.VIEW_BANNER,
    permissions.CREATE_BANNER,
    permissions.EDIT_BANNER,
    permissions.DELETE_BANNER,
    
    // Review permissions
    permissions.BROWSE_REVIEWS,
    permissions.VIEW_REVIEW,
    permissions.APPROVE_REVIEW,
    permissions.DELETE_REVIEW,
    
    // Support ticket permissions
    permissions.BROWSE_SUPPORT_TICKETS,
    permissions.VIEW_TICKET,
    permissions.CREATE_SUPPORT_TICKET,
    permissions.EDIT_SUPPORT_TICKET,
    permissions.ASSIGN_SUPPORT_TICKET,
    
    // System permissions
    permissions.MANAGE_ROLES_AND_PERMISSIONS,
    permissions.MANAGE_SETTINGS,
    permissions.MANAGE_CURRENCY_SETTINGS,
    permissions.MANAGE_MAINTENANCE_MODE,
    permissions.MANAGE_BLOG_POSTS_AND_CONTENT,
    permissions.MANAGE_HOMEPAGE_BANNERS,
    
    // Analytics permissions
    permissions.VIEW_SALES_REPORTS,
    permissions.VIEW_TAX_REPORT,
    permissions.ANALYZE_BUYER_BEHAVIOR,
    
    // Marketing permissions
    permissions.MANAGE_DISCOUNT_CODES_AND_CAMPAIGNS,
    permissions.MANAGE_EMAIL_MARKETING_CAMPAIGNS,
    
    // Content permissions
    permissions.CREATE_AND_EDIT_INFORMATIONAL_PAGES,
  ],
  
  [MANAGER]: [
    // Order permissions
    permissions.BROWSE_ORDERS,
    permissions.VIEW_ORDER,
    permissions.EDIT_ORDER,
    permissions.DELIVER_ORDER,
    permissions.MANAGE_SHIPMENT_TRACKING,
    
    // Product permissions
    permissions.BROWSE_PRODUCTS,
    permissions.VIEW_PRODUCT,
    permissions.EDIT_PRODUCT,
    permissions.MANAGE_PRODUCT_VARIANTS,
    
    // User permissions
    permissions.BROWSE_USERS,
    permissions.VIEW_USER,
    permissions.EDIT_USER,
    
    // Vendor permissions
    permissions.BROWSE_VENDORS,
    permissions.VIEW_VENDOR,
    permissions.EDIT_VENDOR,
    
    // Category permissions
    permissions.BROWSE_CATEGORIES,
    permissions.VIEW_CATEGORY,
    permissions.EDIT_CATEGORY,
    
    // Shipment permissions
    permissions.BROWSE_SHIPMENTS,
    permissions.VIEW_SHIPMENT,
    permissions.EDIT_SHIPMENT,
    
    // Coupon permissions
    permissions.BROWSE_COUPONS,
    permissions.VIEW_COUPON,
    permissions.EDIT_COUPON,
    
    // Banner permissions
    permissions.BROWSE_BANNERS,
    permissions.VIEW_BANNER,
    permissions.EDIT_BANNER,
    
    // Review permissions
    permissions.BROWSE_REVIEWS,
    permissions.VIEW_REVIEW,
    
    // Support ticket permissions
    permissions.BROWSE_SUPPORT_TICKETS,
    permissions.VIEW_TICKET,
    permissions.EDIT_SUPPORT_TICKET,
    permissions.ASSIGN_SUPPORT_TICKET,
    
    // Analytics permissions
    permissions.VIEW_SALES_REPORTS,
    permissions.VIEW_TAX_REPORT,
  ],
  
  [EDITOR]: [
    // Product permissions
    permissions.BROWSE_PRODUCTS,
    permissions.VIEW_PRODUCT,
    permissions.EDIT_PRODUCT,
    
    // Category permissions
    permissions.BROWSE_CATEGORIES,
    permissions.VIEW_CATEGORY,
    permissions.EDIT_CATEGORY,
    
    // Banner permissions
    permissions.BROWSE_BANNERS,
    permissions.VIEW_BANNER,
    permissions.EDIT_BANNER,
    
    // Review permissions
    permissions.BROWSE_REVIEWS,
    permissions.VIEW_REVIEW,
    
    // Content permissions
    permissions.CREATE_AND_EDIT_INFORMATIONAL_PAGES,
  ],
  
  [CUSTOMER_SUPPORT]: [
    // Order permissions
    permissions.BROWSE_ORDERS,
    permissions.VIEW_ORDER,
    
    // User permissions
    permissions.BROWSE_USERS,
    permissions.VIEW_USER,
    
    // Support ticket permissions
    permissions.BROWSE_SUPPORT_TICKETS,
    permissions.VIEW_TICKET,
    permissions.EDIT_SUPPORT_TICKET,
  ],
  
  [VIEWER]: [
    // Order permissions
    permissions.BROWSE_ORDERS,
    permissions.VIEW_ORDER,
    
    // Product permissions
    permissions.BROWSE_PRODUCTS,
    permissions.VIEW_PRODUCT,
    
    // User permissions
    permissions.BROWSE_USERS,
    permissions.VIEW_USER,
    
    // Vendor permissions
    permissions.BROWSE_VENDORS,
    permissions.VIEW_VENDOR,
    
    // Category permissions
    permissions.BROWSE_CATEGORIES,
    permissions.VIEW_CATEGORY,
  ],
};

/**
 * Get role display name
 * @param {string} role - Role key
 * @returns {string} - Display name for the role
 */
export const getRoleDisplayName = (role) => {
  const displayNames = {
    [SUPER_ADMIN]: 'Super Admin',
    [ADMIN]: 'Admin',
    [MANAGER]: 'Manager',
    [EDITOR]: 'Editor',
    [CUSTOMER_SUPPORT]: 'Customer Support',
    [VIEWER]: 'Viewer',
  };
  
  return displayNames[role] || role;
};

/**
 * Get all available roles
 * @returns {Array} - Array of role objects with key and displayName
 */
export const getAllRoles = () => {
  return [
    { key: SUPER_ADMIN, displayName: getRoleDisplayName(SUPER_ADMIN) }, //getRoleDisplayName(SUPER_ADMIN)
    // { key: ADMIN, displayName: getRoleDisplayName(ADMIN) },
    // { key: MANAGER, displayName: getRoleDisplayName(MANAGER) },
    // { key: EDITOR, displayName: getRoleDisplayName(EDITOR) },
    // { key: CUSTOMER_SUPPORT, displayName: getRoleDisplayName(CUSTOMER_SUPPORT) },
    // { key: VIEWER, displayName: getRoleDisplayName(VIEWER) },
  ];
};
