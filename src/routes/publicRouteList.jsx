import { lazy } from 'react';

// Lazy loaded pages
const Login = lazy(() => import('../pages/auth/Login'));
const Register = lazy(() => import('../pages/auth/Register'));

/**
 * Public routes configuration
 * These routes are accessible without authentication
 */
const publicRoutes = [
  {
    path: '/login',
    component: Login,
  },
  {
    path: '/register',
    component: Register,
  },
  {
    path: '/',
    redirect: '/login',
  },
];

export default publicRoutes;
