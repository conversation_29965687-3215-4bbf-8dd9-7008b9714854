import { Routes, Route, Navigate } from 'react-router-dom';
import { Suspense } from 'react';

// Layouts
import PublicLayout from '../layouts/public/PublicLayout';
import PrivateLayout from '../layouts/private/PrivateLayout';

// Components
import ProtectedRoute from '../components/ProtectedRoute';
import PermissionRoute from '../components/PermissionRoute';
import LoadingScreen from '../components/ui/LoadingScreen';

// Route configurations
import publicRouteList from './publicRouteList.jsx';
import privateRouteList from './privateRouteList.jsx';

/**
 * AppRoutes component that defines all application routes
 * Uses route configurations from separate files
 */
const AppRoutes = () => {
  return (
    <Routes>
      {/* Public Routes */}
      <Route element={<PublicLayout />}>
        {publicRouteList.map((route, index) =>
          route.redirect ? (
            <Route
              key={`public-route-${index}`}
              path={route.path}
              element={<Navigate to={route.redirect} replace />}
            />
          ) : (
            <Route
              key={`public-route-${index}`}
              path={route.path}
              element={
                <Suspense fallback={<LoadingScreen />}>
                  <route.component />
                </Suspense>
              }
            />
          )
        )}
      </Route>

      {/* Protected Routes */}
      <Route element={<ProtectedRoute />}>
        <Route element={<PrivateLayout />}>
          {privateRouteList.map((route, index) => (
            <Route
              key={`private-route-${index}`}
              path={route.path}
              element={
                <Suspense fallback={<LoadingScreen />}>
                  {route.permissions ? (
                    <PermissionRoute permissions={route.permissions} redirectTo="/dashboard">
                      <route.component />
                    </PermissionRoute>
                  ) : (
                    <route.component />
                  )}
                </Suspense>
              }
            />
          ))}
        </Route>
      </Route>

      {/* Fallback Route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default AppRoutes;
