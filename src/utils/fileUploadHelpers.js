import api from '@/config/axios';

// Allowed file extensions
const ALLOWED_EXTENSIONS = [
  'jpg', 'jpeg', 'png', 'webp', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv'
];

/**
 * Validate file extension.
 * @param {File} file
 * @returns {boolean}
 */
function isValidFileType(file) {
  const ext = file.name.split('.').pop().toLowerCase();
  return ALLOWED_EXTENSIONS.includes(ext);
}

/**
 * Upload a single file to /api/file-upload
 * @param {File} file
 * @returns {Promise<any>}
 */
export async function uploadSingleFile(file) {
  if (!isValidFileType(file)) {
    throw new Error('Invalid file type');
  }
  const formData = new FormData();
  formData.append('file', file);

  return api.post('/file-upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * Upload multiple files to /api/multiple-file-upload
 * @param {FileList|File[]} files
 * @returns {Promise<any>}
 */
export async function uploadMultipleFiles(files) {
  const formData = new FormData();
  for (const file of files) {
    if (!isValidFileType(file)) {
      throw new Error(`Invalid file type: ${file.name}`);
    }
    formData.append('files', file);
  }

  return api.post('/api/multiple-file-upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}