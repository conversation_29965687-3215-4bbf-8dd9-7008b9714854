/**
 * Generates a URL-friendly slug from text
 * @param {string} text - The text to convert to slug
 * @returns {string} The generated slug
 */
export const generateSlug = (text = '') => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove non-word chars
    .replace(/\s+/g, '-')     // Replace spaces with -
    .replace(/--+/g, '-')     // Replace multiple - with single -
    .trim();                  // Trim leading/trailing -
};