/**
 * Generate mock product data for demo purposes
 * @param {number} count - Number of products to generate
 * @returns {Array} Array of product objects
 */
export const generateMockProducts = (count = 50) => {
  const categories = ['Electronics', 'Clothing', 'Home & Kitchen', 'Books', 'Toys'];
  const statuses = ['In Stock', 'Low Stock', 'Out of Stock', 'Discontinued'];
  
  return Array.from({ length: count }, (_, index) => {
    const id = index + 1;
    const category = categories[Math.floor(Math.random() * categories.length)];
    const price = (Math.random() * 1000).toFixed(2);
    const rating = (Math.random() * 5).toFixed(1);
    const stock = Math.floor(Math.random() * 100);
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    return {
      id,
      name: `Product ${id}`,
      category,
      price: parseFloat(price),
      rating: parseFloat(rating),
      stock,
      status,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000).toISOString(),
    };
  });
};

/**
 * Filter products based on search term and filters
 * @param {Array} products - Array of product objects
 * @param {string} searchTerm - Search term
 * @param {Object} filters - Filter criteria
 * @returns {Array} Filtered array of product objects
 */
export const filterProducts = (products, searchTerm, filters) => {
  return products.filter(product => {
    // Search term filter
    if (searchTerm && !product.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    
    // Category filter
    if (filters.categories.length > 0 && !filters.categories.includes(product.category)) {
      return false;
    }
    
    // Status filter
    if (filters.statuses.length > 0 && !filters.statuses.includes(product.status)) {
      return false;
    }
    
    return true;
  });
};

/**
 * Paginate products
 * @param {Array} products - Array of product objects
 * @param {number} page - Current page number
 * @param {number} itemsPerPage - Number of items per page
 * @returns {Array} Paginated array of product objects
 */
export const paginateProducts = (products, page, itemsPerPage) => {
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return products.slice(startIndex, endIndex);
};
