/**
 * Paginate users
 * @param {Array} users - Array of user objects
 * @param {number} page - Current page number
 * @param {number} itemsPerPage - Number of items per page
 * @returns {Array} Paginated array of user objects
 */
export const paginateUsers = (users, page, itemsPerPage) => {
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return users?.slice(startIndex, endIndex);
};
