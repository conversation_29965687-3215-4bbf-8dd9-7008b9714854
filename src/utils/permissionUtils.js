/**
 * Get permissions for the current user from localStorage.
 * @returns {Array} - Array of permission strings.
 */
const getUserPermissions = () => {
  try {
    const perms = localStorage.getItem('all_permissions');
    return perms ? JSON.parse(perms) : [];
  } catch {
    return [];
  }
};

/**
 * Check if a user has a specific permission
 * @param {Object} user - User object
 * @param {string} permission - Permission to check
 * @returns {boolean} - Whether the user has the permission
 */
export const hasPermission = (user, permission) => {
  if (!user) return false;

  // Super admin has all permissions
  if (user.role === 'admin') return true;

  // Get permissions from localStorage
  const permissions = getUserPermissions();

  // Check if the user has the specific permission
  return permissions.includes(permission);
};

/**
 * Check if a user has any of the specified permissions
 * @param {Object} user - User object
 * @param {Array} permissions - Array of permissions to check
 * @returns {boolean} - Whether the user has any of the permissions
 */
export const hasAnyPermission = (user, permissions) => {
  return true
  if (!user || !permissions || permissions.length === 0) return false;

  // Super admin has all permissions
  if (user.role === 'admin') return true;

  // Get permissions from localStorage
  const userPermissions = getUserPermissions();

  // Check each permission
  return permissions.some(permission => userPermissions.includes(permission));
};

/**
 * Check if a user has all of the specified permissions
 * @param {Object} user - User object
 * @param {Array} permissions - Array of permissions to check
 * @returns {boolean} - Whether the user has all of the permissions
 */
export const hasAllPermissions = (user, permissions) => {
  if (!user || !permissions || permissions.length === 0) return false;

  // Super admin has all permissions
  if (user.role === 'admin') return true;

  // Get permissions from localStorage
  const userPermissions = getUserPermissions();

  // Check each permission
  return permissions.every(permission => userPermissions.includes(permission));
};
