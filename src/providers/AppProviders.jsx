import { Provider } from 'react-redux';
import { I18nextProvider } from 'react-i18next';
import { Browser<PERSON>outer as Router } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import store from '../store';
import i18n from '../i18n/i18n';
import { LanguageProvider } from '../contexts/LanguageContext';
import { PermissionProvider } from '../contexts/PermissionContext';
import { queryClient } from '../services/queryClient';

/**
 * AppProviders component that wraps the application with all necessary providers
 * This includes Redux, i18n, and custom context providers
 */
const AppProviders = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        <I18nextProvider i18n={i18n}>
          <LanguageProvider>
            <PermissionProvider>
              <Router>
                {children}
              </Router>
            </PermissionProvider>
          </LanguageProvider>
        </I18nextProvider>
      </Provider>
    </QueryClientProvider>
  );
};

export default AppProviders;
