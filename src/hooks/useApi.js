// Unified API hook for GET (query), POST, PUT, DELETE (mutations)
import { useQuery, useMutation } from '@tanstack/react-query';
import apiRequest, { get, post, postForm, put, putForm, delete as del } from '../services/apiService';

// GET/query functionality
export const fetchData = (endpoint, params = {}, options = {}) => {
  const queryKey = [endpoint, params];
  const queryFn = async () => {
    const response = await get(endpoint, params);
    return response?.data || response;
  };
  const query = useQuery({
    queryKey,
    queryFn,
    ...options,
  });
  return {
    ...query,
    data: query.data,
    error: query.error,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    isSuccess: query.isSuccess,
    isError: query.isError,
    refetch: query.refetch,
  };
};

// Main hook for all API methods
export const useApi = () => {
  // POST
  const postMutation = useMutation({
    mutationFn: ({ endpoint, data }) => post(endpoint, data).then(res => res.data),
    onError: (error) => {
      console.error('POST error:', error);
      throw error;
    }
  });

  // PUT
  const putMutation = useMutation({
    mutationFn: ({ endpoint, data }) => put(endpoint, data).then(res => res.data),
    onError: (error) => {
      console.error('PUT error:', error);
      throw error;
    }
  });

  // DELETE
  const deleteMutation = useMutation({
    mutationFn: ({ endpoint, data }) => del(endpoint, data).then(res => res.data),
    onError: (error) => {
      console.error('DELETE error:', error);
      throw error;
    }
  });

  return {
    fetchData,
    post,
    postForm,
    put,
    putForm,
    delete: del,
    postMutation,
    putMutation,
    deleteMutation,
  };
};