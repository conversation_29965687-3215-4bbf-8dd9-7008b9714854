import { useRef, useCallback } from "react";
import { generateSlug } from "@/utils/slugUtils";

export const useSlugSync = () => {
    const timerRef = useRef(null);

    return useCallback((e, setFieldValue, nameField, slugField) => {
        const value = e.target.value;
        setFieldValue(nameField, value);

        if (timerRef.current) {
            clearTimeout(timerRef.current);
        }

        timerRef.current = setTimeout(() => {
            const slug = generateSlug(value);
            setFieldValue(slugField, slug);
        }, 1000);
    }, []);
};



// previous code 

// import { useCallback } from "react";
// import { generateSlug } from "@/utils/slugUtils";

// export const useSlugSync = () => {
//     return useCallback((e, setFieldValue, nameField, slugField) => {
//         const value = e.target.value;
//         setFieldValue(nameField, value);
//         setFieldValue(slugField, generateSlug(value));
//     }, []);
// };