// Refactored to use fetchData from useApi.js
import { fetchData } from "../useApi";

/**
 * Custom hook to fetch and manage the list of roles.
 * Returns: { roles, loading, error, refetch }
 */
const useRoleList = () => {
  // Use fetchData to get roles
  const { data, isLoading, error, refetch } = fetchData("admin/roles?pagination=false");

  // The API returns { data: [...] }, so extract roles array
  const roles = data?.data || [];

  return { roles, loading: isLoading, error, refetch };
};

export default useRoleList;