import { fetchData } from "../useApi";

const useBrandList = () => {
    const { data, isLoading, error, refetch } = fetchData("general/brands/active-list?pagination=false");

    const options = data?.data
        ? [
            { label: "Select a Brand", value: "" },
            ...data.data.map((item) => ({
                label: item.name_en,
                value: String(item.id),
            })),
        ]
        : [];

    return { options, loading: isLoading, error, refetch };
};

export default useBrandList;
