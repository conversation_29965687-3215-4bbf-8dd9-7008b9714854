import { fetchData } from "@/hooks/useApi";

const useProductSubCategoryList = (categoryId) => {
    const enabled = !!categoryId;

    const {
        data,
        isLoading,
        error,
        refetch,
    } = fetchData(
        categoryId ? `general/sub-categories/by-category/${categoryId}` : "",
        {},
        { enabled }
    );

    const options = data?.data
        ? data.data.map((sub) => ({
            label: sub.name,
            value: String(sub.id),
        }))
        : [];

    return { options, loading: isLoading, error, refetch };
};

export default useProductSubCategoryList;
