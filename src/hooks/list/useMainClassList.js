import { fetchData } from "../useApi";

const useMainClassList = (categoryId) => {
    const enabled = !!categoryId;

    const {
        data,
        isLoading,
        error,
        refetch,
    } = fetchData(
        categoryId ? `general/classes/by-category/${categoryId}` : "",
        {},
        { enabled }
    );

    const options = data?.data
        ? data.data.map((item) => ({
            label: item.name,
            value: String(item.id),
        }))
        : [];

    return { options, loading: isLoading, error, refetch };
};

export default useMainClassList;
