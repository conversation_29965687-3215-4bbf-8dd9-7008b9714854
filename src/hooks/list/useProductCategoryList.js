import { fetchData } from "../useApi";

const useProductCategoryList = () => {
    const { data, isLoading, error, refetch } = fetchData("general/categories/active-list?pagination=false");

    const options = data?.data
        ? [
            { label: "Select a category", value: "" },
            ...data.data.map((item) => ({
                label: item.name,
                value: String(item.id),
            })),
        ]
        : [];

    return { options, loading: isLoading, error, refetch };
};

export default useProductCategoryList;
