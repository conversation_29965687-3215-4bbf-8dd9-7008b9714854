// Custom hook to fetch and manage the list of permissions
import { fetchData } from "../useApi";

/**
 * Custom hook to fetch and manage the list of permissions.
 * Returns: { permissions, loading, error, refetch }
 */
const usePermissionList = () => {
  // Use fetchData to get permissions
  const { data, isLoading, error, refetch } = fetchData("admin/permissions?pagination=false");

  // The API returns { data: [...] }, so extract permissions array
  const permissions = data?.data || [];

  return { permissions, loading: isLoading, error, refetch };
};

export default usePermissionList;