


import { fetchData } from "../useApi";

const useProductClassList = (categoryId, subCategoryId) => {
    // Build the API URL dynamically based on categoryId and subCategoryId
    const url = `general/classes/active-list?category_id=${categoryId}&sub_category_id=${subCategoryId}&pagination=false`;

    const { data, isLoading, error, refetch } = fetchData(url);

    const options = data?.data
        ? [
            { label: "Select a class", value: "" },
            ...data.data.map((item) => ({
                label: item.name,
                value: String(item.id),
            })),
        ]
        : [];

    return { options, loading: isLoading, error, refetch };
};

export default useProductClassList;
