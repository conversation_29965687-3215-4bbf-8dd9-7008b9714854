
import { fetchData } from "@/hooks/useApi";

const useProductSubClassList = (classId) => {
    const enabled = !!classId;

    const {
        data,
        isLoading,
        error,
        refetch,
    } = fetchData(
        classId ? `general/sub-classes/by-class/${classId}` : "",
        {},
        { enabled }
    );

    const options = data?.data
        ? data.data.map((sub) => ({
            label: sub.name,
            value: String(sub.id),
        }))
        : [];

    return { options, loading: isLoading, error, refetch };
};

export default useProductSubClassList;
