/**
 * Filter users based on search term and filters
 * @param {Array} users - Array of user objects
 * @param {string} searchTerm - Search term
 * @param {Object} filters - Filter criteria
 * @returns {Array} Filtered array of user objects
 */
export const filterUsers = (users, searchTerm, filters) => {
  return users?.filter(user => {
    // Search term filter (name or email)
    if (
      searchTerm &&
      !user.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
      !user.email.toLowerCase().includes(searchTerm.toLowerCase())
    ) {
      return false;
    }

    // Role filter
    if (filters.roles.length > 0 && !filters.roles.includes(user.role)) {
      return false;
    }

    // Status filter
    if (filters.statuses.length > 0 && !filters.statuses.includes(user.status)) {
      return false;
    }

    return true;
  });
};