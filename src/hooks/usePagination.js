import { useState } from 'react';

export default function usePagination(initialPage = 1, initialPageSize = 10, onPageChange) {
  const [page, setPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  const handlePageChange = (newPage) => {
    setPage(newPage);
    if (typeof onPageChange === 'function') {
      onPageChange(newPage, pageSize);
    }
  };

  const handlePageSizeChange = (newSize) => {
    setPageSize(newSize);
    setPage(1);
    if (typeof onPageChange === 'function') {
      onPageChange(1, newSize);
    }
  };

  const resetPagination = () => {
    setPage(initialPage);
    setPageSize(initialPageSize);
    if (typeof onPageChange === 'function') {
      onPageChange(initialPage, initialPageSize);
    }
  };

  return {
    page,
    pageSize,
    setPage: handlePageChange,
    setPageSize: handlePageSizeChange,
    resetPagination,
  };
}