import { createContext, useContext } from 'react';
import { useSelector } from 'react-redux';
import { hasPermission, hasAnyPermission, hasAllPermissions } from '../utils/permissionUtils';

// Create the permission context
const PermissionContext = createContext({
  hasPermission: () => false,
  hasAnyPermission: () => false,
  hasAllPermissions: () => false,
});

/**
 * Permission Provider component
 * Provides permission checking functions to its children
 */
export const PermissionProvider = ({ children }) => {
  const { user } = useSelector((state) => state.auth);
  
  // Create the context value
  const contextValue = {
    hasPermission: (permission) => hasPermission(user, permission),
    hasAnyPermission: (permissions) => hasAnyPermission(user, permissions),
    hasAllPermissions: (permissions) => hasAllPermissions(user, permissions),
  };
  
  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  );
};

/**
 * Custom hook to use the permission context
 * @returns {Object} - Permission context value
 */
export const usePermission = () => {
  const context = useContext(PermissionContext);
  
  if (!context) {
    throw new Error('usePermission must be used within a PermissionProvider');
  }
  
  return context;
};

export default PermissionContext;
