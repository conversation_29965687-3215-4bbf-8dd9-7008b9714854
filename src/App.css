#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* RTL Support Classes */
html[dir="rtl"] .rtl-text-right {
  text-align: right;
}

html[dir="rtl"] .rtl-text-left {
  text-align: left;
}

html[dir="ltr"] .rtl-text-right {
  text-align: left;
}

html[dir="ltr"] .rtl-text-left {
  text-align: right;
}

/* Flex direction classes for RTL support */
html[dir="rtl"] .rtl-flex-row-reverse {
  flex-direction: row-reverse;
}

html[dir="rtl"] .rtl-justify-end {
  justify-content: flex-end;
}

/* Padding classes for RTL support */
html[dir="rtl"] .rtl-pl-0 {
  padding-left: 0;
}

html[dir="rtl"] .rtl-pr-0 {
  padding-right: 0;
}

html[dir="rtl"] .rtl-mr-auto {
  margin-right: auto;
}

html[dir="rtl"] .rtl-ml-auto {
  margin-left: auto;
}

html[dir="ltr"] .rtl-mr-auto {
  margin-left: auto;
}

html[dir="ltr"] .rtl-ml-auto {
  margin-right: auto;
}

/* RTL specific classes */

/* Margin classes for RTL support */
html[dir="rtl"] .rtl-ml-1 {
  margin-left: 0.25rem;
}

html[dir="rtl"] .rtl-mr-1 {
  margin-right: 0.25rem;
}

html[dir="rtl"] .rtl-ml-3 {
  margin-left: 0.75rem;
}

html[dir="rtl"] .rtl-mr-3 {
  margin-right: 0.75rem;
}

/* Original styles */
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
