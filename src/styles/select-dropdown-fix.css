/* Fix for Select dropdown z-index issues */

/* Ensure Radix Select Portal has the highest z-index */
[data-radix-select-content] {
  z-index: 9999 !important;
  position: fixed !important;
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* Ensure viewport has solid background */
[data-radix-select-viewport] {
  background-color: white !important;
  padding: 0.25rem !important;
}

/* Ensure items are visible and interactive */
[data-radix-select-item] {
  background-color: transparent !important;
  color: #111827 !important;
  padding: 0.375rem 0.5rem 0.375rem 2rem !important;
  position: relative !important;
  cursor: default !important;
  user-select: none !important;
  border-radius: 0.125rem !important;
  outline: none !important;
}

[data-radix-select-item]:hover {
  background-color: #f9fafb !important;
}

[data-radix-select-item][data-highlighted] {
  background-color: #eff6ff !important;
  color: #1e40af !important;
}

[data-radix-select-item][data-state="checked"] {
  background-color: #eff6ff !important;
  color: #1e40af !important;
}

/* Fix for the trigger button */
[data-radix-select-trigger] {
  background-color: white !important;
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
}

[data-radix-select-trigger]:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5) !important;
}

/* Ensure the portal container has proper stacking */
[data-radix-portal] {
  z-index: 9999 !important;
}

/* Additional fix for any overlay issues */
.radix-select-content {
  z-index: 9999 !important;
  background: white !important;
  opacity: 1 !important;
}

/* Fix for mobile devices */
@media (max-width: 768px) {
  [data-radix-select-content] {
    z-index: 10000 !important;
    max-height: 50vh !important;
  }
}
