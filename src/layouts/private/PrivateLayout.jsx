import { Outlet } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { useContext } from 'react';
import Sidebar from './Sidebar';
import Header from './Header';
import { LanguageContext } from '../../contexts/LanguageContext';

/**
 * PrivateLayout component that wraps authenticated pages
 * Contains the sidebar, header, and main content area
 */
const PrivateLayout = () => {
  const { sidebarExpanded } = useSelector((state) => state.ui);
  const { direction } = useContext(LanguageContext);
  const isRtl = direction === 'rtl';

  return (
    <div className="min-h-screen bg-gray-50 flex overflow-hidden">
      <Sidebar />

      <div className={`flex-1 flex flex-col transition-all duration-300 ${
        isRtl
          ? (sidebarExpanded ? 'mr-[240px]' : 'mr-[64px]')
          : (sidebarExpanded ? 'ml-[240px]' : 'ml-[64px]')
      }`}>
        <Header />

        <main className="flex-1 p-4 overflow-auto pt-[72px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={window.location.pathname}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="h-full"
            >
              <Outlet />
            </motion.div>
          </AnimatePresence>
        </main>
      </div>
    </div>
  );
};

export default PrivateLayout;
