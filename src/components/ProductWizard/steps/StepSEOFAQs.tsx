import React from 'react';
import { Input } from '@/components/ui/product/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/product/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/product/card';
import { Plus, X } from 'lucide-react';
import BilingualField from '../components/BilingualField';

interface FAQ {
  id: string;
  question_en: string;
  question_ar: string;
  answer_en: string;
  answer_ar: string;
}

interface StepSEOFAQsProps {
  data: any;
  onChange: (data: any) => void;
  layoutMode: 'horizontal' | 'vertical';
}

export default function StepSEOFAQs({ data, onChange, layoutMode }: StepSEOFAQsProps) {
  const handleChange = (field: string, value: any) => {
    onChange({ ...data, [field]: value });
  };

  const handleSEOChange = (field: string, value: any) => {
    onChange({
      ...data,
      product_seo: {
        ...data.product_seo,
        [field]: value
      }
    });
  };

  // Auto-generate slug from title
  React.useEffect(() => {
    if (data.title_en && !data.slug) {
      const slug = data.title_en
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
      handleChange('slug', slug);
    }
  }, [data.title_en]);

  const addFAQ = () => {
    const newFAQ = {
      question: '',
      answer: '',
    };
    const currentFAQs = data.product_faqs || [];
    onChange({
      ...data,
      product_faqs: [...currentFAQs, newFAQ]
    });
  };

  const removeFAQ = (index: number) => {
    const updatedFAQs = (data.product_faqs || []).filter((_: any, i: number) => i !== index);
    onChange({
      ...data,
      product_faqs: updatedFAQs
    });
  };

  const updateFAQ = (index: number, field: string, value: string) => {
    const updatedFAQs = (data.product_faqs || []).map((faq: any, i: number) =>
      i === index ? { ...faq, [field]: value } : faq
    );
    onChange({
      ...data,
      product_faqs: updatedFAQs
    });
  };

  return (
    <div className="space-y-4">
      {/* SEO & Metadata Card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">SEO & Metadata</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <BilingualField
            label="Meta Title"
            layoutMode={layoutMode}
            valueEn={data.product_seo?.meta_title_en || ""}
            valueAr={data.product_seo?.meta_title_ar || ""}
            onChangeEn={(value) => handleSEOChange('meta_title_en', value)}
            onChangeAr={(value) => handleSEOChange('meta_title_ar', value)}
            placeholder={{
              en: "SEO optimized title (60 chars max)",
              ar: "عنوان محسن لمحركات البحث (60 حرف كحد أقصى)"
            }}
          />

          <BilingualField
            label="Meta Description"
            type="textarea"
            layoutMode={layoutMode}
            valueEn={data.product_seo?.meta_description_en || ""}
            valueAr={data.product_seo?.meta_description_ar || ""}
            onChangeEn={(value) => handleSEOChange('meta_description_en', value)}
            onChangeAr={(value) => handleSEOChange('meta_description_ar', value)}
            placeholder={{
              en: "Brief description for search results (160 chars max)",
              ar: "وصف مختصر لنتائج البحث (160 حرف كحد أقصى)"
            }}
          />

          <BilingualField
            label="Keywords"
            layoutMode={layoutMode}
            valueEn={data.product_seo?.keywords_en || ""}
            valueAr={data.product_seo?.keywords_ar || ""}
            onChangeEn={(value) => handleSEOChange('keywords_en', value)}
            onChangeAr={(value) => handleSEOChange('keywords_ar', value)}
            placeholder={{
              en: "keyword1, keyword2, keyword3",
              ar: "كلمة مفتاحية1، كلمة مفتاحية2، كلمة مفتاحية3"
            }}
          />

          <div className="space-y-2">
            <Label htmlFor="slug" className="text-sm">URL Slug</Label>
            <Input
              id="slug"
              value={data.slug}
              onChange={(e) => handleChange('slug', e.target.value)}
              placeholder="product-url-slug"
              className="h-9"
            />
            <p className="text-xs text-gray-500">
              Auto-generated from product title. You can customize it if needed.
            </p>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg">
            <h4 className="text-sm font-medium text-blue-800 mb-2">SEO Preview</h4>
            <div className="space-y-1">
              <div className="text-blue-600 font-medium text-sm">
                {data.product_seo?.meta_title_en || data.title_en || 'Product Title'}
              </div>
              <div className="text-green-600 text-xs">
                example.com/products/{data.slug || 'product-slug'}
              </div>
              <div className="text-gray-600 text-xs">
                {data.product_seo?.meta_description_en || data.short_description_en || 'Product description will appear here...'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* FAQs Card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Frequently Asked Questions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="bg-cyan-50 p-3 rounded-lg border border-cyan-200">
            <p className="text-cyan-600 text-sm">
              Add common questions and answers about your product in both languages
            </p>
          </div>

          <div className="flex justify-center">
            <Button
              onClick={addFAQ}
              className="flex items-center gap-2 h-9"
              variant="outline"
              size="sm"
            >
              <Plus className="w-4 h-4" />
              Add FAQ
            </Button>
          </div>

          <div className="space-y-3">
            {(data.product_faqs || []).map((faq: any, index: number) => (
              <Card key={index} className="border border-gray-200">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm">FAQ #{index + 1}</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFAQ(index)}
                    className="text-red-600 hover:text-red-700 h-8 w-8 p-0"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor={`question_${index}`} className="text-sm">Question</Label>
                    <Input
                      id={`question_${index}`}
                      value={faq.question || ""}
                      onChange={(e) => updateFAQ(index, 'question', e.target.value)}
                      placeholder="What is the question?"
                      className="h-9"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`answer_${index}`} className="text-sm">Answer</Label>
                    <textarea
                      id={`answer_${index}`}
                      value={faq.answer || ""}
                      onChange={(e) => updateFAQ(index, 'answer', e.target.value)}
                      placeholder="Provide a detailed answer..."
                      className="w-full min-h-[80px] px-3 py-2 border border-gray-300 rounded-md text-sm resize-vertical"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {(!data.product_faqs || data.product_faqs.length === 0) && (
            <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
              <Plus className="mx-auto h-8 w-8 text-gray-400" />
              <p className="mt-2 text-sm text-gray-600">
                No FAQs added yet. Click "Add FAQ" to get started.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
