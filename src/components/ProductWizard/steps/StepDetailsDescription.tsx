
import React from 'react';
import { Input } from '@/components/ui/product/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/product/card';
import BilingualField from '../components/BilingualField';

interface StepDetailsDescriptionProps {
  data: any;
  onChange: (data: any) => void;
  layoutMode: 'horizontal' | 'vertical';
  validationErrors?: Record<string, string>;
  showValidation?: boolean;
}

export default function StepDetailsDescription({
  data,
  onChange,
  layoutMode,
  validationErrors,
  showValidation = false
}: StepDetailsDescriptionProps) {
  const handleChange = (field: string, value: any) => {
    onChange({ ...data, [field]: value });
  };

  // Validation for mandatory fields
  const isTitleEnValid = data.title_en;
  const isDescriptionEnValid = data.description_en;
  const isKeyIngredientsValid = data.key_ingredients;
  const isUsageInstructionsValid = data.usage_instructions;
  const isUserGroupValid = data.user_group;
  const isNetWeightValid = data.net_weight;
  const isNetWeightUnitValid = data.net_weight_unit;
  const isFormulationValid = data.formulation;

  const allMandatoryFieldsValid = isTitleEnValid && isDescriptionEnValid && isKeyIngredientsValid &&
    isUsageInstructionsValid && isUserGroupValid && isNetWeightValid && isNetWeightUnitValid && isFormulationValid;
  const shouldShowValidationHint = showValidation && !allMandatoryFieldsValid;

  // Get missing mandatory fields for error message
  const getMissingFields = () => {
    const missing = [];
    if (!isTitleEnValid) missing.push('Product Title (English)');
    if (!isDescriptionEnValid) missing.push('Full Description (English)');
    if (!isKeyIngredientsValid) missing.push('Key Ingredients');
    if (!isUsageInstructionsValid) missing.push('Usage Instructions');
    if (!isUserGroupValid) missing.push('User Group');
    if (!isNetWeightValid) missing.push('Net Weight');
    if (!isNetWeightUnitValid) missing.push('Net Weight Unit');
    if (!isFormulationValid) missing.push('Formulation');
    return missing;
  };

  return (
    <div className="space-y-6">

      {/* Basic Info Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <BilingualField
            label="Product Title"
            required
            layoutMode={layoutMode}
            valueEn={data.title_en}
            valueAr={data.title_ar}
            onChangeEn={(value) => handleChange('title_en', value)}
            onChangeAr={(value) => handleChange('title_ar', value)}
            placeholder={{
              en: "Enter product title in English *",
              ar: "أدخل عنوان المنتج بالعربية"
            }}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="short_name">Short Name</Label>
              <Input
                id="short_name"
                value={data.short_name || ''}
                onChange={(e) => handleChange('short_name', e.target.value)}
                placeholder="Short display name"
              />
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Product Descriptions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <BilingualField
              label="Short Description"
              type="textarea"
              layoutMode={layoutMode}
              valueEn={data.short_description_en}
              valueAr={data.short_description_ar}
              onChangeEn={(value) => handleChange('short_description_en', value)}
              onChangeAr={(value) => handleChange('short_description_ar', value)}
              placeholder={{
                en: "Brief product description (max 160 characters)",
                ar: "وصف مختصر للمنتج (حد أقصى 160 حرف)"
              }}
          />

          <BilingualField
              label="Full Description"
              type="textarea"
              required
              layoutMode={layoutMode}
              valueEn={data.description_en}
              valueAr={data.description_ar}
              onChangeEn={(value) => handleChange('description_en', value)}
              onChangeAr={(value) => handleChange('description_ar', value)}
              placeholder={{
                en: "Detailed product description, features, and benefits *",
                ar: "وصف مفصل للمنتج والميزات والفوائد"
              }}
          />
        </CardContent>
      </Card>
      {/* Description Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Additional Product Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <BilingualField
              label="Key Ingredients *"
              type="textarea"
              layoutMode={layoutMode}
              valueEn={data.key_ingredients}
              valueAr={data.key_ingredients_ar}
              onChangeEn={(value) => handleChange('key_ingredients', value)}
              onChangeAr={(value) => handleChange('key_ingredients_ar', value)}
              placeholder={{
                en: "Main ingredients or components",
                ar: "المكونات أو المكونات الرئيسية"
              }}
          />

          <BilingualField
              label="Usage Instructions *"
              type="textarea"
              layoutMode={layoutMode}
              valueEn={data.usage_instructions}
              valueAr={data.usage_instructions_ar}
              onChangeEn={(value) => handleChange('usage_instructions', value)}
              onChangeAr={(value) => handleChange('usage_instructions_ar', value)}
              placeholder={{
                en: "How to use this product",
                ar: "كيفية استخدام هذا المنتج"
              }}
          />

          {/* Additional Mandatory Fields */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="user_group">User Group *</Label>
                <Select value={data.user_group || ''} onValueChange={(value) => handleChange('user_group', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select target user group" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="men">Men</SelectItem>
                    <SelectItem value="women">Women</SelectItem>
                    <SelectItem value="unisex">Unisex Gender Neutral</SelectItem>
                    <SelectItem value="senior_adults">Senior Adults</SelectItem>
                    <SelectItem value="kids">Kids</SelectItem>
                    <SelectItem value="teens">Teens</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="net_weight_unit">Net Weight Unit *</Label>
                <Select value={data.net_weight_unit || ''} onValueChange={(value) => handleChange('net_weight_unit', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select units" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="kg">Kilograms (kg)</SelectItem>
                    <SelectItem value="g">Grams (g)</SelectItem>
                    <SelectItem value="mg">Milligrams (mg)</SelectItem>
                    <SelectItem value="l">Liters (L)</SelectItem>
                    <SelectItem value="ml">Milliliters (ml)</SelectItem>
                    <SelectItem value="pieces">Pieces</SelectItem>
                    <SelectItem value="cm">Centimeters (cm)</SelectItem>
                    <SelectItem value="m">Meters (m)</SelectItem>
                    <SelectItem value="inches">Inches</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="net_weight">Net Weight *</Label>
                <Input
                  id="net_weight"
                  type="number"
                  step="0.001"
                  value={data.net_weight || ''}
                  onChange={(e) => handleChange('net_weight', e.target.value)}
                  placeholder="Net weight (numeric value)"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="formulation">Formulation *</Label>
                <Select value={data.formulation || ''} onValueChange={(value) => handleChange('formulation', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select formulation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tablet">Tablet</SelectItem>
                    <SelectItem value="capsule">Capsule</SelectItem>
                    <SelectItem value="powder">Powder</SelectItem>
                    <SelectItem value="liquid">Liquid</SelectItem>
                    <SelectItem value="cream">Cream</SelectItem>
                    <SelectItem value="gel">Gel</SelectItem>
                    <SelectItem value="spray">Spray</SelectItem>
                    <SelectItem value="drops">Drops</SelectItem>
                    <SelectItem value="syrup">Syrup</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="flavour">Flavour</Label>
                <Select value={data.flavour || ''} onValueChange={(value) => handleChange('flavour', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select flavour" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="vanilla">Vanilla</SelectItem>
                    <SelectItem value="chocolate">Chocolate</SelectItem>
                    <SelectItem value="strawberry">Strawberry</SelectItem>
                    <SelectItem value="banana">Banana</SelectItem>
                    <SelectItem value="orange">Orange</SelectItem>
                    <SelectItem value="lemon">Lemon</SelectItem>
                    <SelectItem value="mint">Mint</SelectItem>
                    <SelectItem value="berry">Berry</SelectItem>
                    <SelectItem value="unflavored">Unflavored</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="servings">Servings</Label>
                <Input
                  id="servings"
                  type="number"
                  value={data.servings || ''}
                  onChange={(e) => handleChange('servings', parseInt(e.target.value) || 0)}
                  placeholder="Number of servings"
                />
              </div>
            </div>
          </div>

          {shouldShowValidationHint && (
            <div
              className="mt-4 p-4 bg-red-50 rounded-lg border border-red-200 shadow-sm"
              role="alert"
              aria-live="polite"
              aria-describedby="description-validation-message"
            >
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0">⚠</div>
                <div>
                  <h5 className="text-sm font-semibold text-red-800 mb-1">
                    Validation Error
                  </h5>
                  <p
                    id="description-validation-message"
                    className="text-sm text-red-700"
                  >
                    Please fill in the following required fields: {getMissingFields().join(', ')}
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
