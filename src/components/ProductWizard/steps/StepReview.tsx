
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/product/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/product/button';
import { CheckCircle, AlertCircle, XCircle, Edit, Star, Package, Truck, Search, HelpCircle } from 'lucide-react';

interface StepReviewProps {
  data: any;
  onChange: (data: any) => void;
  layoutMode: 'horizontal' | 'vertical';
  onStepClick?: (stepId: number) => void;
}

export default function StepReview({ data, onChange, layoutMode, onStepClick }: StepReviewProps) {
  const validateSection = (sectionName: string, fields: string[]) => {
    const filledFields = fields.filter(field => {
      const value = data[field];
      return value !== '' && value !== 0 && value !== false && value !== null && value !== undefined;
    });
    
    const percentage = (filledFields.length / fields.length) * 100;
    return {
      filled: filledFields.length,
      total: fields.length,
      percentage: Math.round(percentage),
      status: percentage >= 80 ? 'complete' : percentage >= 50 ? 'partial' : 'incomplete'
    };
  };

  const sections = [
    {
      id: 1,
      name: 'Classification',
      icon: Package,
      fields: ['category_id', 'brand_id', 'vendor_sku', 'barcode'],
      validation: validateSection('Classification', ['category_id', 'brand_id', 'vendor_sku'])
    },
    {
      id: 2,
      name: 'Product Details',
      icon: Edit,
      fields: ['product_name_en', 'product_name_ar', 'short_description_en', 'short_description_ar'],
      validation: validateSection('Details', ['product_name_en', 'product_name_ar'])
    },
    {
      id: 3,
      name: 'Product Media',
      icon: Star,
      fields: ['media'],
      validation: {
        filled: data.media?.length || 0,
        total: 1,
        percentage: data.media?.length > 0 ? 100 : 0,
        status: data.media?.length > 0 ? 'complete' : 'incomplete'
      }
    },
    {
      id: 4,
      name: 'Pricing & Inventory',
      icon: Package,
      fields: ['selling_price', 'cost_price', 'stock_quantity'],
      validation: validateSection('Pricing', ['selling_price'])
    },
    {
      id: 5,
      name: 'Compliance & Fulfillment',
      icon: Truck,
      fields: ['mode', 'is_vegan', 'is_vegetarian', 'is_halal'],
      validation: validateSection('Compliance', ['mode'])
    },
    {
      id: 6,
      name: 'SEO & FAQs',
      icon: Search,
      fields: ['meta_title_en', 'meta_description_en', 'slug', 'faqs'],
      validation: {
        filled: [data.meta_title_en, data.slug, data.faqs?.length].filter(Boolean).length,
        total: 3,
        percentage: Math.round(([data.meta_title_en, data.slug, data.faqs?.length].filter(Boolean).length / 3) * 100),
        status: [data.meta_title_en, data.slug, data.faqs?.length].filter(Boolean).length >= 2 ? 'complete' :
                [data.meta_title_en, data.slug, data.faqs?.length].filter(Boolean).length >= 1 ? 'partial' : 'incomplete'
      }
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'partial':
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      default:
        return <XCircle className="w-5 h-5 text-red-600" />;
    }
  };

  const getStatusBadge = (status: string, percentage: number) => {
    const variants = {
      complete: 'default',
      partial: 'secondary',
      incomplete: 'destructive'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants]} className="text-xs">
        {percentage}% Complete
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* Step Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sections.map((section) => {
          const IconComponent = section.icon;
          return (
            <Card key={section.name} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onStepClick?.(section.id)}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <IconComponent className="w-4 h-4" />
                    {section.name}
                  </CardTitle>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Edit className="w-3 h-3" />
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  {getStatusIcon(section.validation.status)}
                  {getStatusBadge(section.validation.status, section.validation.percentage)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xs text-gray-600">
                  {section.validation.filled} of {section.validation.total} fields completed
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Classification & Identity Details */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Classification & Identity</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Category</div>
              <div className="text-sm">{data.category_id || 'Not selected'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Brand</div>
              <div className="text-sm">{data.brand_id || 'Not selected'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Product Code (SKU)</div>
              <div className="text-sm font-mono">{data.vendor_sku || 'Not set'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Barcode</div>
              <div className="text-sm font-mono">{data.barcode || 'Not set'}</div>
            </div>
          </div>
          {data.system_sku && (
            <div className="p-3 bg-green-50 rounded-lg border border-green-200">
              <div className="text-xs font-medium text-green-800">Auto-generated System SKU</div>
              <div className="text-green-700 font-mono text-sm">{data.system_sku}</div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Product Details & Descriptions */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Product Details & Descriptions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Product Name (English)</div>
              <div className="text-sm">{data.product_name_en || 'Not set'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Product Name (Arabic)</div>
              <div className="text-sm" dir="rtl">{data.product_name_ar || 'غير محدد'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Short Description (English)</div>
              <div className="text-sm">{data.short_description_en || 'Not set'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Short Description (Arabic)</div>
              <div className="text-sm" dir="rtl">{data.short_description_ar || 'غير محدد'}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Product Media */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Product Media</CardTitle>
        </CardHeader>
        <CardContent>
          {data.media?.length > 0 ? (
            <div className="space-y-3">
              <div className="text-xs text-gray-600">{data.media.length} image(s) uploaded</div>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                {data.media.map((item: any, index: number) => (
                  <div key={index} className="relative">
                    <img
                      src={item.url}
                      alt={item.alt_text || `Product image ${index + 1}`}
                      className="w-full h-20 object-cover rounded border"
                    />
                    {item.is_primary && (
                      <div className="absolute top-1 left-1 bg-blue-600 text-white px-1 py-0.5 rounded text-xs">
                        <Star className="w-3 h-3" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-sm text-gray-500">No images uploaded</div>
          )}
        </CardContent>
      </Card>

      {/* Pricing & Inventory */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Pricing & Inventory</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Selling Price</div>
              <div className="text-sm font-semibold text-green-600">
                {data.selling_price ? `AED ${data.selling_price}` : 'Not set'}
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Cost Price</div>
              <div className="text-sm">{data.cost_price ? `AED ${data.cost_price}` : 'Not set'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Stock Quantity</div>
              <div className="text-sm">{data.stock_quantity || 'Not set'}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Compliance & Fulfillment */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Compliance & Fulfillment</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Fulfillment Mode</div>
              <div className="text-sm">{data.mode || 'Not selected'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Returns Accepted</div>
              <div className="text-sm">{data.is_returnable ? 'Yes' : 'No'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Shipping Time</div>
              <div className="text-sm">{data.shipping_time || 'Not set'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Shipping Fee</div>
              <div className="text-sm">{data.shipping_fee ? `AED ${data.shipping_fee}` : 'Not set'}</div>
            </div>
          </div>

          {/* Dietary Certifications */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-600">Dietary Certifications</div>
            <div className="flex gap-2">
              {data.is_vegan && <Badge variant="secondary" className="text-xs">Vegan</Badge>}
              {data.is_vegetarian && <Badge variant="secondary" className="text-xs">Vegetarian</Badge>}
              {data.is_halal && <Badge variant="secondary" className="text-xs">Halal</Badge>}
              {!data.is_vegan && !data.is_vegetarian && !data.is_halal && (
                <span className="text-sm text-gray-500">None selected</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SEO & FAQs */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">SEO & FAQs</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Meta Title (English)</div>
              <div className="text-sm">{data.meta_title_en || 'Not set'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Meta Title (Arabic)</div>
              <div className="text-sm" dir="rtl">{data.meta_title_ar || 'غير محدد'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">URL Slug</div>
              <div className="text-sm font-mono text-blue-600">{data.slug || 'Not generated'}</div>
            </div>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-600">Keywords</div>
              <div className="text-sm">{data.keywords_en || 'Not set'}</div>
            </div>
          </div>

          {/* FAQs */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-600">Frequently Asked Questions</div>
            {data.faqs?.length > 0 ? (
              <div className="space-y-2">
                <div className="text-xs text-gray-600">{data.faqs.length} FAQ(s) added</div>
                <div className="space-y-1">
                  {data.faqs.slice(0, 3).map((faq: any, index: number) => (
                    <div key={index} className="p-2 bg-gray-50 rounded text-xs">
                      <div className="font-medium">{faq.question_en || faq.question_ar}</div>
                    </div>
                  ))}
                  {data.faqs.length > 3 && (
                    <div className="text-xs text-gray-500">+{data.faqs.length - 3} more FAQs</div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-sm text-gray-500">No FAQs added</div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Submission Notice */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <div className="flex items-start gap-3">
          <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-800">Ready to Submit?</h4>
            <p className="text-blue-600 text-sm mt-1">
              Your product will be reviewed by our team before going live.
              We recommend completing at least 80% of all sections for faster approval.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
