import React from 'react';
import { Input } from '@/components/ui/product/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/product/card';

interface StepComplianceFulfillmentProps {
  data: any;
  onChange: (data: any) => void;
  layoutMode: 'horizontal' | 'vertical';
  validationErrors?: Record<string, string>;
  showValidation?: boolean;
}

export default function StepComplianceFulfillment({
  data,
  onChange,
  layoutMode,
  validationErrors,
  showValidation = false
}: StepComplianceFulfillmentProps) {
  const handleChange = (field: string, value: any) => {
    onChange({ ...data, [field]: value });
  };

  const handleFulfillmentChange = (field: string, value: any) => {
    onChange({
      ...data,
      product_fulfillment: {
        ...data.product_fulfillment,
        [field]: value
      }
    });
  };

  // Validation for mandatory fields
  const isVeganValid = data.is_vegan !== undefined && data.is_vegan !== null;
  const isVegetarianValid = data.is_vegetarian !== undefined && data.is_vegetarian !== null;
  const isHalalValid = data.is_halal !== undefined && data.is_halal !== null;
  const isAllergenInfoValid = data.allergen_info && data.allergen_info.trim().length > 0;
  const isModeValid = data.product_fulfillment?.mode && data.product_fulfillment.mode.trim().length > 0;
  const isCollectionPointValid = data.product_fulfillment?.collection_point && data.product_fulfillment.collection_point.trim().length > 0;
  const isReturnableValid = data.product_fulfillment?.is_returnable !== undefined && data.product_fulfillment?.is_returnable !== null;

  const allMandatoryFieldsValid = isVeganValid && isVegetarianValid && isHalalValid &&
    isAllergenInfoValid && isModeValid && isCollectionPointValid && isReturnableValid;
  const shouldShowValidationHint = showValidation && !allMandatoryFieldsValid;

  // Get missing mandatory fields for error message
  const getMissingFields = () => {
    const missing = [];
    if (!isVeganValid) missing.push('Vegan');
    if (!isVegetarianValid) missing.push('Vegetarian');
    if (!isHalalValid) missing.push('Halal');
    if (!isAllergenInfoValid) missing.push('Allergen Information');
    if (!isModeValid) missing.push('Fulfillment Mode');
    if (!isCollectionPointValid) missing.push('Collection Point');
    if (!isReturnableValid) missing.push('Returnable');
    return missing;
  };

  return (
    <div className="space-y-4">
      {/* Product Compliance Card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Product Compliance</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-3">
           <div className="space-y-2">
            <Label htmlFor="dietary_needs" className="text-sm">Dietary Needs</Label>
            <Select value={data.dietary_needs} onValueChange={(value) => handleChange('dietary_needs', value)}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Select dietary needs (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="vegan">Vegan</SelectItem>
                <SelectItem value="gluten_free">Gluten Free</SelectItem>
                <SelectItem value="sugar_free">Sugar Free</SelectItem>
                <SelectItem value="dairy_free">Dairy Free</SelectItem>
                <SelectItem value="nut_free">Nut Free</SelectItem>
                <SelectItem value="soy_free">Soy Free</SelectItem>
                <SelectItem value="organic">Organic</SelectItem>
                <SelectItem value="non_gmo">Non-GMO</SelectItem>
                <SelectItem value="keto">Keto</SelectItem>
                <SelectItem value="paleo">Paleo</SelectItem>
                <SelectItem value="low_carb">Low Carb</SelectItem>
                <SelectItem value="high_protein">High Protein</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="flex items-center space-x-2">
              <Switch
                id="is_vegan"
                checked={data.is_vegan}
                onCheckedChange={(checked) => handleChange('is_vegan', checked)}
              />
              <Label htmlFor="is_vegan" className="text-sm">Vegan *</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="is_vegetarian"
                checked={data.is_vegetarian}
                onCheckedChange={(checked) => handleChange('is_vegetarian', checked)}
              />
              <Label htmlFor="is_vegetarian" className="text-sm">Vegetarian *</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="is_halal"
                checked={data.is_halal}
                onCheckedChange={(checked) => handleChange('is_halal', checked)}
              />
              <Label htmlFor="is_halal" className="text-sm">Halal *</Label>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="allergen_info" className="text-sm">Allergen Information *</Label>
            <Textarea
              id="allergen_info"
              value={data.allergen_info}
              onChange={(e) => handleChange('allergen_info', e.target.value)}
              placeholder="Contains nuts, dairy, gluten, etc. (or 'None' if no allergens)"
              rows={3}
              className="text-sm"
            />
          </div>
        </CardContent>
      </Card>

      {/* Storage & Origin Card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Storage & Origin</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
              <Label htmlFor="storage_conditions" className="text-sm">Storage Conditions</Label>
              <Input
                id="storage_conditions"
                value={data.storage_conditions}
                onChange={(e) => handleChange('storage_conditions', e.target.value)}
                placeholder="Room temperature, refrigerated, etc."
                className="h-9"
              />
            </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        
            <div className="space-y-2">
            <Label htmlFor="vat_tax_url" className="text-sm">VAT Tax URL</Label>
            <Input
              id="vat_tax_url"
              value={data.vat_tax_url || ""}
              onChange={(e) => handleChange('vat_tax_url', e.target.value)}
              placeholder="https://example.com/vat-certificate"
              className="h-9"
            />
          </div>

           <div className="space-y-2">
              <Label htmlFor="regulatory_product_registration" className="text-sm">Product Registration No.</Label>
              <Input
                id="regulatory_product_registration"
                value={data.regulatory_product_registration}
                onChange={(e) => handleChange('regulatory_product_registration', e.target.value)}
                placeholder="Regulatory registration number"
                className="h-9"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="space-y-2">
              <Label htmlFor="country_of_origin" className="text-sm">Country of Origin</Label>
              <Input
                id="country_of_origin"
                value={data.country_of_origin}
                onChange={(e) => handleChange('country_of_origin', e.target.value)}
                placeholder="Saudi Arabia"
                className="h-9"
              />
            </div>
        
            <div className="space-y-2">
              <Label htmlFor="bbe_date" className="text-sm">Best Before Date</Label>
              <Input
                id="bbe_date"
                type="date"
                value={data.bbe_date}
                onChange={(e) => handleChange('bbe_date', e.target.value)}
                className="h-9"
              />
            </div>
          </div>

      
        </CardContent>
      </Card>

      {/* Fulfillment Method Card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Fulfillment Method</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <Label htmlFor="mode" className="text-sm">Fulfillment Mode *</Label>
            <Select value={data.product_fulfillment?.mode || ""} onValueChange={(value) => handleFulfillmentChange('mode', value)}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Select fulfillment mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="fba">FBA</SelectItem>
                <SelectItem value="drop_ship">Drop Ship</SelectItem>
                <SelectItem value="self_ship">Self Ship</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is_returnable"
              checked={data.product_fulfillment?.is_returnable || false}
              onCheckedChange={(checked) => handleFulfillmentChange('is_returnable', checked)}
            />
            <Label htmlFor="is_returnable" className="text-sm">Returnable *</Label>
          </div>

          <div className="space-y-2">
            <Label htmlFor="collection_point" className="text-sm">Collection Point *</Label>
            <Select value={data.product_fulfillment?.collection_point || ""} onValueChange={(value) => handleFulfillmentChange('collection_point', value)}>
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Select collection point" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Warehouse">Warehouse</SelectItem>
                <SelectItem value="Distribution Center">Distribution Center</SelectItem>
                <SelectItem value="Pickup Point">Pickup Point</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Shipping Information Card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-gray-700">Shipping Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="space-y-2">
              <Label htmlFor="shipping_time" className="text-sm">Shipping Time (days)</Label>
              <Input
                id="shipping_time"
                type="number"
                value={data.product_fulfillment?.shipping_time || ""}
                onChange={(e) => handleFulfillmentChange('shipping_time', parseInt(e.target.value) || 0)}
                placeholder="3"
                className="h-9"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="shipping_fee" className="text-sm">Shipping Fee (AED)</Label>
              <Input
                id="shipping_fee"
                type="number"
                step="0.01"
                value={data.product_fulfillment?.shipping_fee || ""}
                onChange={(e) => handleFulfillmentChange('shipping_fee', parseFloat(e.target.value) || 0)}
                placeholder="15.99"
                className="h-9"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
            <div className="space-y-2">
              <Label htmlFor="package_length" className="text-sm">Length (cm)</Label>
              <Input
                id="package_length"
                type="number"
                step="0.1"
                value={data.package_length}
                onChange={(e) => handleChange('package_length', parseFloat(e.target.value) || 0)}
                placeholder="0.0"
                className="h-9"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="package_width" className="text-sm">Width (cm)</Label>
              <Input
                id="package_width"
                type="number"
                step="0.1"
                value={data.package_width}
                onChange={(e) => handleChange('package_width', parseFloat(e.target.value) || 0)}
                placeholder="0.0"
                className="h-9"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="package_height" className="text-sm">Height (cm)</Label>
              <Input
                id="package_height"
                type="number"
                step="0.1"
                value={data.package_height}
                onChange={(e) => handleChange('package_height', parseFloat(e.target.value) || 0)}
                placeholder="0.0"
                className="h-9"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="package_weight" className="text-sm">Weight (kg)</Label>
              <Input
                id="package_weight"
                type="number"
                step="0.01"
                value={data.package_weight}
                onChange={(e) => handleChange('package_weight', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                className="h-9"
              />
            </div>
          </div>

          {shouldShowValidationHint && (
            <div
              className="mt-4 p-4 bg-red-50 rounded-lg border border-red-200 shadow-sm"
              role="alert"
              aria-live="polite"
              aria-describedby="compliance-validation-message"
            >
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0">⚠</div>
                <div>
                  <h5 className="text-sm font-semibold text-red-800 mb-1">
                    Validation Error
                  </h5>
                  <p
                    id="compliance-validation-message"
                    className="text-sm text-red-700"
                  >
                    Please fill in the following required fields: {getMissingFields().join(', ')}
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
