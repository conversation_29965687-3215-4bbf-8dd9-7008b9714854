
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/product/button';
import { Input } from '@/components/ui/product/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/product/card';
import { Upload, X, Star, GripVertical, ImageIcon, Video, Play, AlertCircle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useApi } from '@/hooks/useApi';

interface StepProductMediaProps {
  data: any;
  onChange: (data: any) => void;
  layoutMode: 'horizontal' | 'vertical';
  validationErrors?: any;
  showValidation?: boolean;
}

interface MediaItem {
  id: string;
  product_id?: number;
  product_variant_id?: number | null;
  type: 'image' | 'video' | 'lifestyle';
  path: string;
  url?: string; // For display purposes
  file?: File;
  title?: string | null;
  alt_text?: string | null;
  lang_code?: string | null;
  position?: number | null;
  is_primary: number; // 0 or 1
  order?: number; // For local ordering
}

interface VideoUrlItem {
  id: string;
  url: string;
  title: string;
  isValid: boolean;
}

export default function StepProductMedia({
  data,
  onChange,
  layoutMode,
  validationErrors = {},
  showValidation = false
}: StepProductMediaProps) {
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [videoUrls, setVideoUrls] = useState<VideoUrlItem[]>([]);
  const [newVideoUrl, setNewVideoUrl] = useState('');
  const [newVideoTitle, setNewVideoTitle] = useState('');

  const fileInputRef = useRef<HTMLInputElement>(null);
  const mainImageInputRef = useRef<HTMLInputElement>(null);

  const { toast } = useToast();
  const { postMutation } = useApi();

  // Ensure media array is always defined to prevent undefined property access
  const mediaArray: MediaItem[] = data.media || [];

  // Separate main image and additional images
  const mainImage = mediaArray.find(item => item.is_primary === 1 && item.type === 'image');
  const additionalImages = mediaArray.filter(item => item.is_primary === 0 && item.type === 'image');
  const videos = mediaArray.filter(item => item.type === 'video');

  const handleChange = (field: string, value: any) => {
    onChange({ ...data, [field]: value });
  };

  // Initialize video URLs from existing data
  useEffect(() => {
    const existingVideos = videos.map(video => ({
      id: video.id,
      url: video.path,
      title: video.title || '',
      isValid: true
    }));
    setVideoUrls(existingVideos);
  }, [videos.length]);

  // Utility functions
  const validateVideoUrl = (url: string): boolean => {
    const videoPatterns = [
      /^https?:\/\/(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)/,
      /^https?:\/\/(www\.)?vimeo\.com\/\d+/,
      /^https?:\/\/.+\.(mp4|webm|ogg)$/i
    ];
    return videoPatterns.some(pattern => pattern.test(url));
  };

  const getProductId = (): number | null => {
    return data.id || data.product_id || null;
  };

  const uploadMediaToAPI = async (mediaItems: MediaItem[]) => {
    const productId = getProductId();
    if (!productId) {
      toast({
        title: "Error",
        description: "Product ID is required for media upload",
        variant: "destructive",
      });
      return false;
    }

    try {
      setIsUploading(true);

      const mediaData = mediaItems.map((item, index) => ({
        product_id: productId,
        product_variant_id: null,
        type: item.type,
        path: item.path,
        title: item.title || null,
        alt_text: item.alt_text || null,
        lang_code: 'en',
        position: item.position || index,
        is_primary: item.is_primary,
      }));

      await postMutation.mutateAsync({
        endpoint: 'admin/product-media/store-update',
        data: { media: mediaData }
      });

      toast({
        title: "Success",
        description: "Media uploaded successfully",
      });

      return true;
    } catch (error) {
      console.error('Media upload failed:', error);
      toast({
        title: "Upload Failed",
        description: "Failed to upload media. Please try again.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileSelect = (files: FileList | null, isMainImage = false) => {
    if (!files) return;

    const maxFiles = isMainImage ? 1 : (6 - mediaArray.length);
    const filesToAdd = Array.from(files).slice(0, maxFiles);

    if (!isMainImage && filesToAdd.length < files.length) {
      toast({
        title: "File limit reached",
        description: `Only ${maxFiles} more images can be added (max 6 total)`,
        variant: "destructive",
      });
    }

    const newMediaItems: MediaItem[] = filesToAdd.map((file, index) => ({
      id: `media_${Date.now()}_${index}`,
      type: 'image' as const,
      path: file.name, // Will be updated after upload
      url: URL.createObjectURL(file),
      file,
      title: null,
      alt_text: null,
      lang_code: 'en',
      position: isMainImage ? 0 : (mediaArray.length + index + 1),
      is_primary: isMainImage ? 1 : 0,
      order: isMainImage ? 0 : (mediaArray.length + index + 1),
    }));

    if (isMainImage) {
      // Remove existing main image and add new one
      const updatedMedia = mediaArray.filter(item => item.is_primary !== 1);
      handleChange('media', [...updatedMedia, ...newMediaItems]);
    } else {
      handleChange('media', [...mediaArray, ...newMediaItems]);
    }
  };

  const handleMainImageUpload = () => {
    mainImageInputRef.current?.click();
  };

  const handleAdditionalImageUpload = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    handleFileSelect(files, false);
  };

  const addVideoUrl = () => {
    if (!newVideoUrl.trim()) {
      toast({
        title: "Invalid URL",
        description: "Please enter a video URL",
        variant: "destructive",
      });
      return;
    }

    if (!validateVideoUrl(newVideoUrl)) {
      toast({
        title: "Invalid Video URL",
        description: "Please enter a valid YouTube, Vimeo, or direct video URL",
        variant: "destructive",
      });
      return;
    }

    const videoItem: MediaItem = {
      id: `video_${Date.now()}`,
      type: 'video',
      path: newVideoUrl,
      title: newVideoTitle.trim() || null,
      alt_text: null,
      lang_code: 'en',
      position: mediaArray.length,
      is_primary: 0,
    };

    handleChange('media', [...mediaArray, videoItem]);

    setVideoUrls([...videoUrls, {
      id: videoItem.id,
      url: newVideoUrl,
      title: newVideoTitle,
      isValid: true
    }]);

    setNewVideoUrl('');
    setNewVideoTitle('');

    toast({
      title: "Video Added",
      description: "Video URL has been added successfully",
    });
  };

  const removeVideoUrl = (id: string) => {
    const updatedMedia = mediaArray.filter(item => item.id !== id);
    const updatedVideoUrls = videoUrls.filter(video => video.id !== id);

    handleChange('media', updatedMedia);
    setVideoUrls(updatedVideoUrls);
  };

  const removeMediaItem = (id: string) => {
    // Find item to remove for cleanup
    let itemToRemove: MediaItem | undefined;
    for (let i = 0; i < mediaArray.length; i++) {
      if (mediaArray[i].id === id) {
        itemToRemove = mediaArray[i];
        break;
      }
    }

    if (itemToRemove?.url && itemToRemove.url.startsWith('blob:')) {
      URL.revokeObjectURL(itemToRemove.url);
    }

    const updatedMedia = mediaArray.filter((item: MediaItem) => item.id !== id);

    // If we removed the primary image, make the first image primary
    if (updatedMedia.length > 0) {
      let hasPrimary = false;
      for (let i = 0; i < updatedMedia.length; i++) {
        if (updatedMedia[i].is_primary === 1) {
          hasPrimary = true;
          break;
        }
      }
      if (!hasPrimary) {
        // Find first image and make it primary
        for (let i = 0; i < updatedMedia.length; i++) {
          if (updatedMedia[i].type === 'image') {
            updatedMedia[i].is_primary = 1;
            break;
          }
        }
      }
    }

    // Reorder items
    const reorderedMedia = updatedMedia.map((item: MediaItem, index: number) => ({
      ...item,
      order: index + 1,
      position: index
    }));

    handleChange('media', reorderedMedia);
  };

  const updateMediaItem = (id: string, field: string, value: any) => {
    const updatedMedia = mediaArray.map((item: MediaItem) =>
      item.id === id ? { ...item, [field]: value } : item
    );
    handleChange('media', updatedMedia);
  };

  const setPrimaryImage = (id: string) => {
    const updatedMedia = mediaArray.map((item: MediaItem) => ({
      ...item,
      is_primary: item.id === id ? 1 : 0
    }));
    handleChange('media', updatedMedia);
  };

  const handleItemDragStart = (e: React.DragEvent, id: string) => {
    setDraggedItem(id);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleItemDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleItemDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    if (!draggedItem || draggedItem === targetId) return;

    // Find indices manually
    let draggedIndex = -1;
    let targetIndex = -1;

    for (let i = 0; i < mediaArray.length; i++) {
      if (mediaArray[i].id === draggedItem) draggedIndex = i;
      if (mediaArray[i].id === targetId) targetIndex = i;
    }

    if (draggedIndex === -1 || targetIndex === -1) return;

    const updatedMedia = [...mediaArray];
    const [draggedMediaItem] = updatedMedia.splice(draggedIndex, 1);
    updatedMedia.splice(targetIndex, 0, draggedMediaItem);

    // Update order numbers
    const reorderedMedia = updatedMedia.map((item: MediaItem, index: number) => ({
      ...item,
      order: index + 1,
      position: index
    }));

    handleChange('media', reorderedMedia);
    setDraggedItem(null);
  };

  return (
    <div className="space-y-8">
      {/* Main Image Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5 text-yellow-500" />
            Main Product Image
            {showValidation && !mainImage && (
              <span className="text-red-500 text-sm ml-2">* Required</span>
            )}
          </CardTitle>
          <p className="text-sm text-gray-600">
            Upload the primary image that will represent your product. This will be the main image shown in listings.
          </p>
        </CardHeader>
        <CardContent>
          <input
            ref={mainImageInputRef}
            type="file"
            accept="image/*"
            className="hidden"
            onChange={(e) => handleFileSelect(e.target.files, true)}
          />

          {mainImage ? (
            <div className="relative inline-block">
              <img
                src={mainImage.url || mainImage.path}
                alt={mainImage.alt_text || 'Main product image'}
                className="w-48 h-48 object-cover rounded-lg border-2 border-yellow-400"
              />
              <div className="absolute top-2 right-2 flex gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleMainImageUpload}
                  className="h-8 w-8 p-0 bg-white"
                >
                  <Upload className="w-4 h-4" />
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => removeMediaItem(mainImage.id)}
                  className="h-8 w-8 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              <div className="absolute bottom-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-medium">
                Primary
              </div>
            </div>
          ) : (
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
              onClick={handleMainImageUpload}
            >
              <ImageIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <div className="space-y-2">
                <Button variant="outline" className="flex items-center gap-2">
                  <Upload className="w-4 h-4" />
                  Upload Main Image
                </Button>
                <p className="text-sm text-gray-500">
                  Click to upload your main product image
                </p>
                <p className="text-xs text-gray-400">
                  Supports: JPG, PNG, GIF, WebP (max 5MB)
                </p>
              </div>
            </div>
          )}

          {mainImage && (
            <div className="mt-4 space-y-2">
              <Label htmlFor="main_alt_text" className="text-sm">
                Alt Text (for accessibility)
              </Label>
              <Input
                id="main_alt_text"
                value={mainImage.alt_text || ''}
                onChange={(e) => updateMediaItem(mainImage.id, 'alt_text', e.target.value)}
                placeholder="Describe the main image..."
                className="text-sm"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Additional Images Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="w-5 h-5 text-blue-500" />
            Additional Product Images
          </CardTitle>
          <p className="text-sm text-gray-600">
            Upload additional images to showcase different angles, details, or lifestyle shots of your product.
          </p>
        </CardHeader>
        <CardContent>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            className="hidden"
            onChange={(e) => handleFileSelect(e.target.files, false)}
          />

          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              isDragging
                ? 'border-blue-400 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <ImageIcon className="mx-auto h-10 w-10 text-gray-400 mb-3" />
            <div className="space-y-2 flex flex-col items-center">
              <Button
                onClick={handleAdditionalImageUpload}
                disabled={additionalImages.length >= 5}
                className="flex items-center gap-2"
                variant="outline"
              >
                <Upload className="w-4 h-4" />
                {additionalImages.length >= 5 ? 'Maximum images reached' : 'Upload Additional Images'}
              </Button>
              <p className="text-sm text-gray-500">
                Drag and drop images here, or click to browse ({additionalImages.length}/5)
              </p>
              <p className="text-xs text-gray-400">
                Supports: JPG, PNG, GIF, WebP (max 5MB each)
              </p>
            </div>
          </div>

          {/* Additional Images Grid */}
          {additionalImages.length > 0 && (
            <div className="mt-6">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">Additional Images</h4>
                <p className="text-sm text-gray-500">
                  Drag to reorder
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {additionalImages.map((item: MediaItem, index: number) => (
                  <Card
                    key={item.id}
                    className={`relative cursor-move transition-transform hover:scale-105 ${
                      draggedItem === item.id ? 'opacity-50' : ''
                    }`}
                    draggable
                    onDragStart={(e) => handleItemDragStart(e, item.id)}
                    onDragOver={handleItemDragOver}
                    onDrop={(e) => handleItemDrop(e, item.id)}
                  >
                    <CardContent className="p-4">
                      <div className="relative mb-4 group">
                        <img
                          src={item.url || item.path}
                          alt={item.alt_text || `Additional image ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />

                        {/* Drag handle */}
                        <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <div className="bg-black bg-opacity-50 rounded p-1">
                            <GripVertical className="w-4 h-4 text-white" />
                          </div>
                        </div>

                        {/* Remove button */}
                        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => removeMediaItem(item.id)}
                            className="h-8 w-8 p-0"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>

                        {/* Order number */}
                        <div className="absolute bottom-2 right-2 bg-gray-900 bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                          #{item.order || index + 1}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`alt_text_${item.id}`} className="text-sm">
                          Alt Text (for accessibility)
                        </Label>
                        <Input
                          id={`alt_text_${item.id}`}
                          value={item.alt_text || ''}
                          onChange={(e) => updateMediaItem(item.id, 'alt_text', e.target.value)}
                          placeholder="Describe this image..."
                          className="text-sm"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Video URL Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="w-5 h-5 text-purple-500" />
            Product Videos
          </CardTitle>
          <p className="text-sm text-gray-600">
            Add video URLs to showcase your product in action. Supports YouTube, Vimeo, and direct video links.
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="video_url" className="text-sm">
                  Video URL
                </Label>
                <Input
                  id="video_url"
                  value={newVideoUrl}
                  onChange={(e) => setNewVideoUrl(e.target.value)}
                  placeholder="https://youtube.com/watch?v=... or https://vimeo.com/..."
                  className="text-sm"
                />
              </div>
              <div>
                <Label htmlFor="video_title" className="text-sm">
                  Video Title (Optional)
                </Label>
                <Input
                  id="video_title"
                  value={newVideoTitle}
                  onChange={(e) => setNewVideoTitle(e.target.value)}
                  placeholder="Product demonstration video"
                  className="text-sm"
                />
              </div>
            </div>

            <Button
              onClick={addVideoUrl}
              disabled={!newVideoUrl.trim() || isUploading}
              className="flex items-center gap-2"
              variant="outline"
            >
              <Video className="w-4 h-4" />
              Add Video
            </Button>
          </div>

          {/* Video List */}
          {videoUrls.length > 0 && (
            <div className="mt-6">
              <h4 className="font-medium text-gray-900 mb-4">Added Videos</h4>
              <div className="space-y-3">
                {videoUrls.map((video) => (
                  <div key={video.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="flex-shrink-0">
                        {video.isValid ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <AlertCircle className="w-5 h-5 text-red-500" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {video.title || 'Untitled Video'}
                        </p>
                        <p className="text-xs text-gray-500 truncate">
                          {video.url}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(video.url, '_blank')}
                          className="h-8 w-8 p-0"
                        >
                          <Play className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => removeVideoUrl(video.id)}
                          className="h-8 w-8 p-0"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Status */}
      {isUploading && (
        <div className="flex items-center justify-center p-4 bg-blue-50 rounded-lg">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm text-blue-600">Uploading media...</span>
          </div>
        </div>
      )}

      {/* Validation Messages */}
      {showValidation && !mainImage && (
        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <AlertCircle className="w-5 h-5 text-red-500" />
          <span className="text-sm text-red-600">
            Please upload at least one main product image.
          </span>
        </div>
      )}
    </div>
  );
}
