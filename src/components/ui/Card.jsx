import { motion } from "framer-motion";

const Card = ({
  children,
  title,
  titleLabel,
  className = "",
  icon,
  animate = true,
  fixedHeight = false,
  action,
  ...props
}) => {
  const cardContent = (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden ${
        fixedHeight ? "h-full flex flex-col" : ""
      } ${className}`}
      {...props}
    >
      {title && (
        <div className="px-5 py-4 border-b border-gray-50 flex items-center justify-between">
          <div className="flex items-center">
            {icon && <span className="mr-3">{icon}</span>}
            <h3 className="font-medium text-gray-800 text-lg">{title}</h3>
          </div>
          {action}
        </div>
      )}
      {titleLabel && (
        <div className="px-5 pt-4 pb-2 border-b border-gray-200 bg-slate-200">
          <div className="flex items-center">
            <h3 className="text-lg font-semibold text-slate-900">
              {titleLabel}
            </h3>
          </div>
        </div>
      )}

      <div className={`p-5 ${fixedHeight ? "flex-grow" : ""}`}>{children}</div>
    </div>
  );

  if (animate) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        {cardContent}
      </motion.div>
    );
  }

  return cardContent;
};

export default Card;
