import { useState, useRef, useEffect } from "react";
import { uploadSingleFile } from "@/utils/fileUploadHelpers";
import LoadingSpinner from "./LoadingSpinner";

const ImageUpload = ({
  value,
  onUploadSuccess,
  onFileSuccess,
  asFile = false,
}) => {
  const [imagePath, setImagePath] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);

  useEffect(() => setImagePath(value), [value]);

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (asFile) {
      setImagePath(URL.createObjectURL(file));
      onFileSuccess?.(file);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await uploadSingleFile(file);
      const url = response.data?.data?.path;

      if (!url) {
        throw new Error("No URL returned from upload");
      }

      setImagePath(response.data?.data?.path);
      setImageUrl(response.data?.data?.full_url)
      onUploadSuccess?.(url);
      fileInputRef.current.value = "";
    } catch (err) {
      setError(err.message || "Failed to upload image");
      console.error("Upload error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  return (
    <div className="space-y-4">
      <div className="mt-4">
        <div
          className="w-32 h-32 border rounded overflow-hidden flex items-center justify-center bg-gray-100 cursor-pointer"
          onClick={triggerFileInput}
        >
          {imagePath ? (
            <div className="relative w-full h-full">
              <img
                src={imageUrl || imagePath}
                alt="Preview"
                className="w-full h-full object-cover"
              />
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  setImagePath("");
                  onUploadSuccess?.(null);
                  fileInputRef.current.value = "";
                }}
                className="absolute top-1 right-1 bg-white rounded-full p-1 shadow-md hover:bg-gray-200"
                aria-label="Remove image"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-red-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          ) : (
            <div className="text-gray-500 text-center p-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-8 w-8 mx-auto mb-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
          )}
        </div>
      </div>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />

      {isLoading && <LoadingSpinner />}

      {error && <p className="text-red-500">{error}</p>}
    </div>
  );
};

export default ImageUpload;
