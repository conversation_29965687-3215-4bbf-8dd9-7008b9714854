import React from "react";
import { useTranslation } from "react-i18next";
import Pagination from "./Pagination";

const PaginationInfo = ({
  itemsPerPage,
  onItemsPerPageChange,
  perPageOptions = [5, 10, 20, 50],
  currentPage,
  totalItems,
  totalPages,
  onPageChange,
  className = "",
  ...paginationProps
}) => {
  const { t } = useTranslation();
  const start = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
  const end = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div
      className={`flex flex-col md:flex-row items-center justify-between ${className}`}
    >
      <div className="flex items-center space-x-2 mb-4 md:mb-0 gap-1">
        <span className="text-sm text-gray-500">
          {t("commonPagination.show")}
        </span>
        <select
          value={itemsPerPage}
          onChange={onItemsPerPageChange}
          className="border border-gray-300 rounded-md text-sm p-1"
        >
          {perPageOptions.map((opt) => (
            <option key={opt} value={opt}>
              {opt}
            </option>
          ))}
        </select>
        <span className="text-sm text-gray-500">
          {t("commonPagination.perPage")}
        </span>
      </div>
      <div className="flex items-center">
        <span className="text-sm text-gray-500 mr-4 gap-2 mx-2">
          {t("commonPagination.showing")} {start} {t("commonPagination.to")}{" "}
          {end} {t("commonPagination.of")} {totalItems}{" "}
          {t("commonPagination.results")}
        </span>
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={onPageChange}
          {...paginationProps}
        />
      </div>
    </div>
  );
};

export default PaginationInfo;
