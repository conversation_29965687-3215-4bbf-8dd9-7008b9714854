import { useField } from "formik";
import Select from "react-select";
import { useState } from "react";

const countries = [
  { code: "AE", label: "United Arab Emirates", dial_code: "+971", flag: "🇦🇪" },
  { code: "US", label: "United States", dial_code: "+1", flag: "🇺🇸" },
  { code: "GB", label: "United Kingdom", dial_code: "+44", flag: "🇬🇧" },
  { code: "IN", label: "India", dial_code: "+91", flag: "🇮🇳" },
  { code: "BD", label: "Bangladesh", dial_code: "+880", flag: "🇧🇩" },
  { code: "PK", label: "Pakistan", dial_code: "+92", flag: "🇵🇰" },
  { code: "LK", label: "Sri Lanka", dial_code: "+94", flag: "🇱🇰" },
];

const FormPhoneNumberInput = ({ label, name, required = false }) => {
  const [field, meta, helpers] = useField(name);
  const error = meta.touched && meta.error;
  const [selectedCountry, setSelectedCountry] = useState(countries[0]);

  const handlePhoneChange = (e) => {
    const rawInput = e.target.value;
    const onlyNumbers = rawInput.replace(/\D/g, "");
    helpers.setValue(`${selectedCountry.dial_code}${onlyNumbers}`);
  };

  return (
    <div className="mb-4">
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}

      <div className="flex gap-2">
        <Select
          className="w-40"
          value={selectedCountry}
          onChange={(val) => {
            setSelectedCountry(val);
            helpers.setValue(`${val.dial_code}`);
          }}
          options={countries}
          getOptionLabel={(e) => (
            <div className="flex items-center gap-2">
              <span>{e.flag}</span>
              <span>{e.code}</span>
              <span>{e.dial_code}</span>
            </div>
          )}
          getOptionValue={(e) => e.code}
        />
        <input
          type="tel"
          className={`flex-1 border rounded px-3 py-2 text-sm ${
            error ? "border-red-500" : "border-gray-300"
          }`}
          placeholder="Enter phone number"
          onChange={handlePhoneChange}
          value={field.value.replace(selectedCountry.dial_code, "")}
        />
      </div>

      {error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
};

export default FormPhoneNumberInput;
