import { useField } from "formik";
import { useState } from "react";
import { motion } from "framer-motion";

const FormTextarea = ({ label, helperText, className = "", ...props }) => {
  const [field, meta, helpers] = useField(props);
  const hasError = meta.touched && meta.error;
  const [isFocused, setIsFocused] = useState(false);

  return (
    <div className={` ${className}`}>
      {label && (
        <label
          htmlFor={props.id || props.name}
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          {label}
          {props.required && (
            <span className="text-red-500 ml-1 rtl-ml-0 rtl-mr-1">*</span>
          )}
        </label>
      )}

      <div className="relative">
        <textarea
          {...field}
          {...props}
          className={`
            w-full px-3 py-2 bg-white border rounded-md shadow-sm text-sm
            transition-all duration-200 ease-in-out
            ${
              hasError
                ? "border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500"
                : "border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            }
            ${isFocused ? "ring-2 ring-indigo-500 border-indigo-500" : ""}
            ${
              props.disabled
                ? "bg-gray-100 text-gray-500 cursor-not-allowed"
                : ""
            }
          `}
          onFocus={() => setIsFocused(true)}
          onBlur={(e) => {
            field.onBlur(e);
            setIsFocused(false);
          }}
          rows={props.rows || 4}
        />
      </div>

      <div className="mt-1">
        {hasError ? (
          <motion.p
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-600"
          >
            {meta.error}
          </motion.p>
        ) : helperText ? (
          <p className="text-xs text-gray-500">{helperText}</p>
        ) : null}
      </div>
    </div>
  );
};

export default FormTextarea;
