import React from "react";
import { Field, useField } from "formik";
import { motion } from "framer-motion";

// not ready icon component

const FormIconInput = ({
  label,
  helperText,
  className = "",
  icon,
  iconLink,
  size = 24,
  color = "gray",
  onChangeIcon, // New prop to handle icon changes
  ...props
}) => {
  const [field, meta] = useField(props);
  const [isFocused, setIsFocused] = React.useState(false);
  const hasError = meta.touched && meta.error;

  // Function to update icon value when user selects a new icon
  const handleIconChange = (newIcon) => {
    // Update the Formik state with the new icon name
    onChangeIcon && onChangeIcon(newIcon);
  };

  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label
          htmlFor={props.id || props.name}
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          {label}
          {props.required && (
            <span className="text-red-500 ml-1 rtl-ml-0 rtl-mr-1">*</span>
          )}
        </label>
      )}

      <div className="relative">
        {icon && (
          <a
            href={iconLink || "#"}
            className="absolute inset-y-0 left-0 rtl-left-auto rtl-right-0 flex items-center pl-3 pointer-events-auto"
            onClick={() => handleIconChange(icon)} // Handle icon click to select it
          >
            <span
              className="material-symbols-outlined text-gray-400"
              style={{ fontSize: size, color }}
            >
              {icon}
            </span>
          </a>
        )}

        <Field
          {...field}
          {...props}
          disabled
          type="text"
          value={icon || ""} // Set the field value to the icon name
          className={`
            w-full pl-${
              icon ? 10 : 3
            } pr-3 py-2 bg-white border rounded-md shadow-sm text-sm
            transition-all duration-200 ease-in-out
            border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500
            ${isFocused ? "ring-2 ring-indigo-500 border-indigo-500" : ""}
            ${
              props.disabled
                ? "bg-gray-100 text-gray-500 cursor-not-allowed"
                : ""
            }
          `}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />
      </div>

      <div className="mt-1">
        {hasError ? (
          <motion.p
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-600"
          >
            {meta.error}
          </motion.p>
        ) : helperText ? (
          <p className="text-xs text-gray-500">{helperText}</p>
        ) : null}
      </div>
    </div>
  );
};

export default FormIconInput;
