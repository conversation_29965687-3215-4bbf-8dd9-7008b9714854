import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";

const Pagination = ({
  currentPage,
  totalPages,
  onPageChange,
  className = "",
}) => {
  const { t } = useTranslation();
  // Generate page numbers to display
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // If total pages is less than or equal to maxPagesToShow, show all pages
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always include first page
      pageNumbers.push(1);

      // Calculate start and end of page numbers to show
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);

      // Adjust if we're at the beginning or end
      if (currentPage <= 2) {
        end = Math.min(totalPages - 1, 4);
      } else if (currentPage >= totalPages - 1) {
        start = Math.max(2, totalPages - 3);
      }

      // Add ellipsis if needed
      if (start > 2) {
        pageNumbers.push("...");
      }

      // Add middle pages
      for (let i = start; i <= end; i++) {
        pageNumbers.push(i);
      }

      // Add ellipsis if needed
      if (end < totalPages - 1) {
        pageNumbers.push("...");
      }

      // Always include last page
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  const pageNumbers = getPageNumbers();

  // Create pagination buttons in the correct order based on direction
  const renderPaginationButtons = () => {
    const prevButton = (
      <motion.button
        whileTap={{ scale: 0.95 }}
        onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`px-3 py-1 rounded-md border order-1 rtl-order-3 ${
          currentPage === 1
            ? "bg-gray-100 text-gray-400 cursor-not-allowed"
            : "bg-white text-gray-700 hover:bg-gray-50"
        }`}
      >
        {t("demo.list.previous")}
      </motion.button>
    );

    const nextButton = (
      <motion.button
        whileTap={{ scale: 0.95 }}
        onClick={() =>
          currentPage < totalPages && onPageChange(currentPage + 1)
        }
        disabled={currentPage === totalPages}
        className={`px-3 py-1 rounded-md border order-3 rtl-order-1 ${
          currentPage === totalPages
            ? "bg-gray-100 text-gray-400 cursor-not-allowed"
            : "bg-white text-gray-700 hover:bg-gray-50"
        }`}
      >
        {t("demo.list.next")}
      </motion.button>
    );

    const pageNumberButtons = pageNumbers?.map((page, index) => (
      <motion.button
        key={`page-${index}`}
        whileTap={{ scale: 0.95 }}
        onClick={() => typeof page === "number" && onPageChange(page)}
        className={`w-8 h-8 flex items-center justify-center rounded-md order-2 ${
          currentPage === page
            ? "bg-indigo-600 text-white"
            : page === "..."
            ? "cursor-default"
            : "bg-white text-gray-700 hover:bg-gray-50"
        }`}
      >
        {page}
      </motion.button>
    ));

    return (
      <>
        {prevButton}
        {pageNumberButtons}
        {nextButton}
      </>
    );
  };

  return (
    <div
      className={`flex items-center justify-center rtl-flex-row-reverse rtl-space-x-reverse my-4 gap-2 ${className}`}
    >
      {renderPaginationButtons()}
    </div>
  );
};

export default Pagination;
