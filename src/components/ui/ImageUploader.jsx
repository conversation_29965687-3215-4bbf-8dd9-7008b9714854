// import React, { useCallback, useState } from "react";
// import { useDropzone } from "react-dropzone";
// import { FaTimes } from "react-icons/fa";

// const ImageUploader = ({ onChange, title = "Upload Image" }) => {
//   const [preview, setPreview] = useState(null);
//   const [file, setFile] = useState(null);
//   const [dropKey, setDropKey] = useState(Date.now());

//   const onDrop = useCallback(
//     (acceptedFiles) => {
//       const imageFile = acceptedFiles[0];
//       if (
//         imageFile &&
//         ["image/jpeg", "image/png", "image/jpg", "image/gif"].includes(
//           imageFile.type
//         )
//       ) {
//         const imageUrl = URL.createObjectURL(imageFile);
//         setPreview(imageUrl);
//         setFile(imageFile);
//         if (onChange) onChange(imageFile);
//       }
//     },
//     [onChange]
//   );

//   const removeImage = () => {
//     setPreview(null);
//     setFile(null);
//     setDropKey(Date.now());
//     if (onChange) onChange(null);
//   };

//   return (
//     <div className="w-full space-y-2" key={dropKey}>
//       <label className="block text-sm font-medium text-gray-700 mb-1">
//         {title}
//       </label>
//       <Dropzone onDrop={onDrop} preview={preview} removeImage={removeImage} />
//     </div>
//   );
// };

// const Dropzone = ({ onDrop, preview, removeImage }) => {
//   const { getRootProps, getInputProps } = useDropzone({
//     onDrop,
//     accept: {
//       "image/jpeg": [],
//       "image/jpg": [],
//       "image/png": [],
//       "image/gif": [],
//     },
//     multiple: false,
//   });

//   return (
//     <div
//       {...getRootProps()}
//       className="border-2 border-dotted border-sky-400 rounded-2xl p-6 flex justify-center items-center flex-col cursor-pointer min-h-[100px] transition-all hover:bg-sky-50 relative"
//     >
//       <input {...getInputProps()} />
//       {preview ? (
//         <>
//           <img
//             src={preview}
//             alt="Preview"
//             className="max-h-24 object-contain rounded-md"
//           />
//           <button
//             type="button"
//             onClick={removeImage}
//             className="absolute top-3 right-3 bg-white text-red-500 rounded-full p-1 hover:bg-red-50 shadow"
//           >
//             <FaTimes className="w-4 h-4" />
//           </button>
//         </>
//       ) : (
//         <>
//           <svg
//             xmlns="http://www.w3.org/2000/svg"
//             className="h-12 w-12 text-sky-400 mb-2"
//             fill="none"
//             viewBox="0 0 24 24"
//             stroke="currentColor"
//             strokeWidth="1.5"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               d="M4 17v2a1 1 0 001 1h14a1 1 0 001-1v-2M7 10l5-5m0 0l5 5m-5-5v12"
//             />
//           </svg>

//           <p className="text-gray-600 font-medium text-center text-sm">
//             Drop .jpg, .jpeg, .png, .gif here or click to upload.
//           </p>
//         </>
//       )}
//     </div>
//   );
// };

// export default ImageUploader;

// import React, { useCallback, useEffect, useState } from "react";
// import { useDropzone } from "react-dropzone";
// import { FaTimes } from "react-icons/fa";

// const ImageUploader = ({ onChange, title = "Upload Image", value }) => {
//   const [preview, setPreview] = useState(null);
//   const [dropKey, setDropKey] = useState(Date.now());

//   useEffect(() => {
//     if (value) {
//       if (typeof value === "string") {
//         setPreview(value);
//       } else if (value?.path) {
//         setPreview(value.path);
//       }
//     } else {
//       setPreview(null);
//     }
//   }, [value]);

//   const onDrop = useCallback(
//     (acceptedFiles) => {
//       const file = acceptedFiles[0];
//       if (
//         file &&
//         ["image/jpeg", "image/png", "image/jpg", "image/gif"].includes(
//           file.type
//         )
//       ) {
//         const imageUrl = URL.createObjectURL(file);
//         setPreview(imageUrl);
//         if (onChange) onChange(file);
//       }
//     },
//     [onChange]
//   );

//   const removeImage = () => {
//     setPreview(null);
//     setDropKey(Date.now());
//     if (onChange) onChange(null);
//   };

//   return (
//     <div className="w-full space-y-2" key={dropKey}>
//       <label className="block text-sm font-medium text-gray-700 mb-1">
//         {title}
//       </label>
//       <Dropzone onDrop={onDrop} preview={preview} removeImage={removeImage} />
//     </div>
//   );
// };

// const Dropzone = ({ onDrop, preview, removeImage }) => {
//   const { getRootProps, getInputProps } = useDropzone({
//     onDrop,
//     accept: {
//       "image/jpeg": [],
//       "image/jpg": [],
//       "image/png": [],
//       "image/gif": [],
//     },
//     multiple: false,
//   });

//   return (
//     <div
//       {...getRootProps()}
//       className="border-2 border-dotted border-sky-400 rounded-2xl p-6 flex justify-center items-center flex-col cursor-pointer min-h-[100px] transition-all hover:bg-sky-50 relative"
//     >
//       <input {...getInputProps()} />
//       {preview ? (
//         <>
//           <img
//             src={preview}
//             alt="Preview"
//             className="max-h-24 object-contain rounded-md"
//           />
//           <button
//             type="button"
//             onClick={removeImage}
//             className="absolute top-3 right-3 bg-white text-red-500 rounded-full p-1 hover:bg-red-50 shadow"
//           >
//             <FaTimes className="w-4 h-4" />
//           </button>
//         </>
//       ) : (
//         <>
//           <svg
//             xmlns="http://www.w3.org/2000/svg"
//             className="h-12 w-12 text-sky-400 mb-2"
//             fill="none"
//             viewBox="0 0 24 24"
//             stroke="currentColor"
//             strokeWidth="1.5"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               d="M4 17v2a1 1 0 001 1h14a1 1 0 001-1v-2M7 10l5-5m0 0l5 5m-5-5v12"
//             />
//           </svg>

//           <p className="text-gray-600 font-medium text-center text-sm">
//             Drop .jpg, .jpeg, .png, .gif here or click to upload.
//           </p>
//         </>
//       )}
//     </div>
//   );
// };

// export default ImageUploader;

import React, { useCallback, useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import { FaTimes } from "react-icons/fa";
import { uploadSingleFile } from "@/utils/fileUploadHelpers";
import LoadingSpinner from "./LoadingSpinner";

const ImageUploader = ({ onChange, title = "Upload Image", value }) => {
  const [preview, setPreview] = useState(null);
  const [dropKey, setDropKey] = useState(Date.now());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    if (value) {
      if (typeof value === "string") {
        setPreview(value);
      } else if (value?.path) {
        setPreview(value.path);
      }
    } else {
      setPreview(null);
    }
  }, [value]);

  const onDrop = useCallback(
    async (acceptedFiles) => {
      const file = acceptedFiles[0];
      if (
        file &&
        ["image/jpeg", "image/png", "image/jpg", "image/gif"].includes(
          file.type
        )
      ) {
        setIsLoading(true);
        setError("");
        try {
          const response = await uploadSingleFile(file);
          const url = response?.data?.data?.full_url;
          const path = response?.data?.data?.path;

          if (!url || !path) {
            throw new Error("Upload failed, no URL returned");
          }

          setPreview(url);
          if (onChange) onChange(path);
        } catch (err) {
          setError(err.message || "Failed to upload image");
          setPreview(null);
          if (onChange) onChange(null);
        } finally {
          setIsLoading(false);
        }
      }
    },
    [onChange]
  );

  const removeImage = () => {
    setPreview(null);
    setDropKey(Date.now());
    if (onChange) onChange(null);
  };

  return (
    <div className="w-full space-y-2" key={dropKey}>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {title}
      </label>
      <Dropzone
        onDrop={onDrop}
        preview={preview}
        removeImage={removeImage}
        isLoading={isLoading}
      />
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
};

const Dropzone = ({ onDrop, preview, removeImage, isLoading }) => {
  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "image/jpeg": [],
      "image/jpg": [],
      "image/png": [],
      "image/gif": [],
    },
    multiple: false,
  });

  return (
    <div
      {...getRootProps()}
      className="border-2 border-dotted border-sky-400 rounded-2xl p-6 flex justify-center items-center flex-col cursor-pointer min-h-[100px] transition-all hover:bg-sky-50 relative"
    >
      <input {...getInputProps()} />
      {isLoading ? (
        <LoadingSpinner />
      ) : preview ? (
        <>
          <img
            src={preview}
            alt="Preview"
            className="max-h-24 object-contain rounded-md"
          />
          <button
            type="button"
            onClick={removeImage}
            className="absolute top-3 right-3 bg-white text-red-500 rounded-full p-1 hover:bg-red-50 shadow"
          >
            <FaTimes className="w-4 h-4" />
          </button>
        </>
      ) : (
        <>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-sky-400 mb-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="1.5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M4 17v2a1 1 0 001 1h14a1 1 0 001-1v-2M7 10l5-5m0 0l5 5m-5-5v12"
            />
          </svg>
          <p className="text-gray-600 font-medium text-center text-sm">
            Drop .jpg, .jpeg, .png, .gif here or click to upload.
          </p>
        </>
      )}
    </div>
  );
};

export default ImageUploader;
