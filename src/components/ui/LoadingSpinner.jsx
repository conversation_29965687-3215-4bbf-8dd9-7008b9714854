import React from "react";

const LoadingSpinner = ({
  size = 40,
  className = "",
  overlay = false,
}) => {
  if (overlay) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
        <div className={`flex items-center justify-center ${className}`}>
          <svg
            className="animate-spin text-indigo-600"
            width={size}
            height={size}
            viewBox="0 0 50 50"
            fill="none"
          >
            <circle
              className="opacity-20"
              cx="25"
              cy="25"
              r="20"
              stroke="currentColor"
              strokeWidth="6"
            />
            <path
              d="M45 25c0-11.046-8.954-20-20-20"
              stroke="currentColor"
              strokeWidth="6"
              strokeLinecap="round"
              className="opacity-80"
            />
          </svg>
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <svg
        className="animate-spin text-indigo-600"
        width={size}
        height={size}
        viewBox="0 0 50 50"
        fill="none"
      >
        <circle
          className="opacity-20"
          cx="25"
          cy="25"
          r="20"
          stroke="currentColor"
          strokeWidth="6"
        />
        <path
          d="M45 25c0-11.046-8.954-20-20-20"
          stroke="currentColor"
          strokeWidth="6"
          strokeLinecap="round"
          className="opacity-80"
        />
      </svg>
      <span className="sr-only">Loading...</span>
    </div>
  );
};

export default LoadingSpinner;