import { Navigate } from 'react-router-dom';
import { usePermission } from '../contexts/PermissionContext';

/**
 * PermissionRoute component
 * Protects routes based on user permissions
 *
 * @param {Object} props - Component props
 * @param {Array} props.permissions - Array of permissions required to access the route
 * @param {string} props.redirectTo - Path to redirect to if user doesn't have permission
 * @param {React.ReactNode} props.children - Child components to render if user has permission
 * @returns {JSX.Element} - Children or Navigate component
 */
const PermissionRoute = ({ permissions, redirectTo = '/dashboard', children }) => {
  const { hasAnyPermission } = usePermission();

  // If no permissions are specified, allow access
  if (!permissions || permissions.length === 0) {
    return children;
  }

  // Check if the user has any of the required permissions
  const hasPermission = hasAnyPermission(permissions);

  // If the user has permission, render the children
  if (hasPermission) {
    return children;
  }

  // Otherwise, redirect to the specified path
  return <Navigate to={redirectTo} replace />;
};

export default PermissionRoute;
