.tiptap.ProseMirror {
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  > img:not([data-type="emoji"] img) {
    margin: 2rem 0;
    outline: 0.125rem solid transparent;
    border-radius: var(--tt-radius-xs, 0.25rem);
  }

  &.ProseMirror-focused
    img:not([data-type="emoji"] img).ProseMirror-selectednode {
    outline-color: var(--tt-brand-color-500);
  }

  // Thread image handling
  .tiptap-thread:has(> img) {
    margin: 2rem 0;

    img {
      outline: 0.125rem solid transparent;
      border-radius: var(--tt-radius-xs, 0.25rem);
    }
  }

  .tiptap-thread img {
    margin: 0;
  }
}
