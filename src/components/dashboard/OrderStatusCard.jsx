import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useState, useMemo } from 'react';
import Card from '../ui/Card';

const OrderStatusCard = ({ orders }) => {
  const { t } = useTranslation();
  const [sortField, setSortField] = useState('id');
  const [sortDirection, setSortDirection] = useState('asc');

  // Function to determine status color for the badge
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'text-amber-600 bg-amber-50 border-amber-200 hover:bg-amber-100 transition-colors';
      case 'processing':
        return 'text-amber-500 bg-amber-50 border-amber-200 hover:bg-amber-100 transition-colors';
      case 'confirmed':
        return 'text-green-500 bg-green-50 border-green-200 hover:bg-green-100 transition-colors';
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200 hover:bg-green-100 transition-colors';
      case 'cancelled':
        return 'text-red-600 bg-red-50 border-red-200 hover:bg-red-100 transition-colors';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 hover:bg-gray-100 transition-colors';
    }
  };

  // Function to determine row background color
  const getRowBgColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-50 hover:bg-amber-100 transition-colors';
      case 'processing':
        return 'bg-amber-50 hover:bg-amber-100 transition-colors';
      case 'confirmed':
        return 'bg-green-50 hover:bg-green-100 transition-colors';
      case 'completed':
        return 'bg-green-50 hover:bg-green-100 transition-colors';
      case 'cancelled':
        return 'bg-red-50 hover:bg-red-100 transition-colors';
      default:
        return 'hover:bg-gray-100 transition-colors';
    }
  };

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      // If already sorting by this field, toggle direction
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // If sorting by a new field, set it and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort the orders based on current sort field and direction
  const sortedOrders = useMemo(() => {
    if (!orders || !sortField) return orders;

    return [...orders].sort((a, b) => {
      if (sortField === 'id') {
        // Parse the ID as a number for numerical sorting
        const idA = parseInt(a.id, 10);
        const idB = parseInt(b.id, 10);
        return sortDirection === 'asc'
          ? idA - idB
          : idB - idA;
      } else if (sortField === 'amount') {
        return sortDirection === 'asc'
          ? a.amount - b.amount
          : b.amount - a.amount;
      } else if (sortField === 'status') {
        return sortDirection === 'asc'
          ? a.status.localeCompare(b.status)
          : b.status.localeCompare(a.status);
      }
      return 0;
    });
  }, [orders, sortField, sortDirection]);

  return (
    <Card title={t('dashboard.details.orderStatus')} fixedHeight={true}>
      <div className="space-y-4 h-full flex flex-col">
        <div className="flex justify-between items-center text-sm font-medium text-gray-500 border-b pb-2">
          <div
            className="w-20 cursor-pointer hover:text-gray-700 flex items-center group relative"
            onClick={() => handleSort('id')}
            title="Click to sort by Order ID"
          >
            <span className="flex items-center whitespace-nowrap">
              Order ID
              <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ml-1 transition-opacity ${sortField === 'id' ? 'opacity-100' : 'opacity-50 group-hover:opacity-100'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {sortField === 'id' && sortDirection === 'asc' ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                )}
              </svg>
            </span>
          </div>
          <div className="flex-1 pl-4">Customer</div>
          <div
            className="w-24 text-center cursor-pointer hover:text-gray-700 flex items-center justify-center group relative"
            onClick={() => handleSort('amount')}
            title="Click to sort by Amount"
          >
            <span className="flex items-center">
              Amount
              <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ml-1 transition-opacity ${sortField === 'amount' ? 'opacity-100' : 'opacity-50 group-hover:opacity-100'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {sortField === 'amount' && sortDirection === 'asc' ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                )}
              </svg>
            </span>
          </div>
          <div
            className="w-24 text-center cursor-pointer hover:text-gray-700 flex items-center justify-center group relative"
            onClick={() => handleSort('status')}
            title="Click to sort by Status"
          >
            <span className="flex items-center">
              Status
              <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ml-1 transition-opacity ${sortField === 'status' ? 'opacity-100' : 'opacity-50 group-hover:opacity-100'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {sortField === 'status' && sortDirection === 'asc' ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                )}
              </svg>
            </span>
          </div>
        </div>

        {sortedOrders && sortedOrders.length > 0 ? (
          <div className="space-y-2 overflow-y-auto flex-grow">
            {sortedOrders.map((order, index) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                className={`flex justify-between items-center p-2 rounded-md transition-colors duration-150 ${
                  getRowBgColor(order.status)
                } hover:bg-opacity-80`}
              >
                <div className="w-20 font-medium text-gray-500 whitespace-nowrap">#{order.id}</div>
                <div className="flex-1 pl-2 font-medium text-gray-700">{order.customer}</div>
                <div className="w-24 text-center font-medium">AED {order.amount.toFixed(2)}</div>
                <div className="w-24 text-center">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(order.status)}`}>
                    {order.status}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-gray-500">No orders found</div>
        )}
      </div>
    </Card>
  );
};

export default OrderStatusCard;
