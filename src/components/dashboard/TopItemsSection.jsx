import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Card from '../ui/Card';
import TimePeriodFilter from './TimePeriodFilter';

const TopItemsSection = ({ title, items, icon, color = 'indigo', className = '', footerTitle }) => {
  const [activePeriod, setActivePeriod] = useState('1d');

  // Get the appropriate data based on the selected time period
  const getItemsData = () => {
    if (!items || !items[activePeriod]) {
      return [];
    }
    return items[activePeriod];
  };

  const currentItems = getItemsData();

  // Calculate total sales for current period
  const totalSales = currentItems.reduce((sum, item) => {
    const valueNumber = parseFloat(item.value.replace(/[^0-9.-]+/g, ''));
    return sum + valueNumber;
  }, 0);

  // Format as currency
  const formattedTotalSales = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'AED',
    maximumFractionDigits: 0
  }).format(totalSales);

  // Get the top item for the header
  const topItem = currentItems.length > 0 ? currentItems[0] : null;

  const colorClasses = {
    indigo: 'bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600',
    blue: 'bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600',
    green: 'bg-gradient-to-br from-green-50 to-green-100 text-green-600',
    red: 'bg-gradient-to-br from-red-50 to-red-100 text-red-600',
    yellow: 'bg-gradient-to-br from-yellow-50 to-yellow-100 text-yellow-600',
    purple: 'bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600',
    orange: 'bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600',
    teal: 'bg-gradient-to-br from-teal-50 to-teal-100 text-teal-600',
  };

  return (
    <Card className={`${className}`} fixedHeight={true}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          {topItem && (
            <p className="mt-1 text-2xl font-bold text-gray-800">{topItem.name}</p>
          )}
        </div>
        {icon && (
          <div className={`p-3 rounded-full shadow-sm ${colorClasses[color]}`}>
            {icon}
          </div>
        )}
      </div>

      <div className="mb-4">
        <TimePeriodFilter activePeriod={activePeriod} onChange={setActivePeriod} />
      </div>

      <div className="space-y-2 flex-grow">
        {currentItems.map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: index * 0.05 }}
            className="flex items-center py-1"
          >
            <span className="w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 text-xs font-medium text-gray-600 mr-3">
              {index + 1}
            </span>
            <span className="text-sm font-medium text-gray-700 flex-grow">{item.name}</span>
            <span className="text-sm font-medium text-gray-700">{item.value}</span>
          </motion.div>
        ))}
      </div>

      {topItem && topItem.ratio && (
        <div className="mt-4 pt-3 border-t border-gray-100">
         <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600">Sales Total</span>
              <span className="text-lg font-bold text-gray-800">{formattedTotalSales}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600">{footerTitle}</span>
              <span className="text-sm font-bold text-gray-800">{topItem.ratio}</span>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default TopItemsSection;
