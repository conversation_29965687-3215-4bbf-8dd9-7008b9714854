import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import Card from '../ui/Card';

const SalesChart = ({ dailyData, weeklyData, monthlyData }) => {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState('daily');

  // Get the appropriate data based on the selected time range
  const getChartData = () => {
    switch (timeRange) {
      case 'daily':
        return dailyData;
      case 'weekly':
        return weeklyData;
      case 'monthly':
        return monthlyData;
      default:
        return dailyData;
    }
  };

  return (
    <Card title={t('dashboard.charts.timeSales')}>
      <div className="mb-4 flex space-x-2">
        <button
          onClick={() => setTimeRange('daily')}
          className={`px-3 py-1 text-sm rounded-md ${
            timeRange === 'daily'
              ? 'bg-indigo-100 text-indigo-700 font-medium'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          Daily
        </button>
        <button
          onClick={() => setTimeRange('weekly')}
          className={`px-3 py-1 text-sm rounded-md ${
            timeRange === 'weekly'
              ? 'bg-indigo-100 text-indigo-700 font-medium'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          Weekly
        </button>
        <button
          onClick={() => setTimeRange('monthly')}
          className={`px-3 py-1 text-sm rounded-md ${
            timeRange === 'monthly'
              ? 'bg-indigo-100 text-indigo-700 font-medium'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          Monthly
        </button>
      </div>

      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={getChartData()}
            margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area type="monotone" dataKey="sales" name="Sales (AED)" stroke="#6366F1" fill="#A5B4FC" />
            <Area type="monotone" dataKey="orders" name="Orders" stroke="#10B981" fill="#A7F3D0" />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
};

export default SalesChart;
