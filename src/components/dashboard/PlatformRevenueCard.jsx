import { useTranslation } from 'react-i18next';
import Card from '../ui/Card';

const PlatformRevenueCard = ({ revenueData, icon }) => {
  const { t } = useTranslation();

  return (
    <Card title={t('dashboard.stats.platformRevenue')} fixedHeight={true}>
      <div className="flex items-center justify-between mb-4">
        <div className="text-3xl font-bold text-gray-800">AED {revenueData.total.toLocaleString()}</div>
        <div className="p-3 rounded-full shadow-sm bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600">
          {icon}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mt-4">
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-sm text-gray-500">{t('dashboard.details.byPlatform')}</div>
          <div className="text-lg font-semibold text-gray-800">
            AED {(revenueData.total - revenueData.fromVendor).toLocaleString()}
          </div>
        </div>
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-sm text-gray-500">{t('dashboard.details.fromVendor')}</div>
          <div className="text-lg font-semibold text-gray-800">
            AED {revenueData.fromVendor.toLocaleString()}
          </div>
        </div>
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-sm text-gray-500">{t('dashboard.details.advertisingRevenue')}</div>
          <div className="text-lg font-semibold text-gray-800">
            AED {revenueData.platformFees.toLocaleString()}
          </div>
        </div>
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-sm text-gray-500">{t('dashboard.details.shippingExpense')}</div>
          <div className="text-lg font-semibold text-gray-800">
            AED {revenueData.shippingRevenue.toLocaleString()}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default PlatformRevenueCard;
