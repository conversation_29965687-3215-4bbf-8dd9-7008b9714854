import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Card from '../ui/Card';
import TimePeriodFilter from './TimePeriodFilter';

const MetricSection = ({ title, metrics, icon, color = 'indigo', className = '' }) => {
  const [activePeriod, setActivePeriod] = useState('1d');

  // Get the appropriate data based on the selected time period
  const getMetricData = () => {
    if (!metrics || !metrics[activePeriod]) {
      return [];
    }
    return metrics[activePeriod];
  };

  const currentMetrics = getMetricData();

  // Get the main metric (first one) and the rest
  const mainMetric = currentMetrics.length > 0 ? currentMetrics[0] : null;
  const detailMetrics = currentMetrics.length > 0 ? currentMetrics.slice(1, -1) : [];

  // Get the summary metric (last one, usually a total or ratio)
  const summaryMetric = currentMetrics.length > 0 ? currentMetrics[currentMetrics.length - 1] : null;

  const colorClasses = {
    indigo: 'bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600',
    blue: 'bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600',
    green: 'bg-gradient-to-br from-green-50 to-green-100 text-green-600',
    red: 'bg-gradient-to-br from-red-50 to-red-100 text-red-600',
    yellow: 'bg-gradient-to-br from-yellow-50 to-yellow-100 text-yellow-600',
    purple: 'bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600',
    orange: 'bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600',
    teal: 'bg-gradient-to-br from-teal-50 to-teal-100 text-teal-600',
  };

  return (
    <Card className={`${className}`} fixedHeight={true}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          {mainMetric && (
            <p className="mt-1 text-2xl font-bold text-gray-800">{mainMetric.value}</p>
          )}
        </div>
        {icon && (
          <div className={`p-3 rounded-full shadow-sm ${colorClasses[color]}`}>
            {icon}
          </div>
        )}
      </div>

      <div className="mb-4">
        <TimePeriodFilter activePeriod={activePeriod} onChange={setActivePeriod} />
      </div>

      <div className="space-y-2 flex-grow">
        {detailMetrics.map((metric, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: index * 0.05 }}
            className="flex justify-between items-center py-1"
          >
            <span className="text-sm text-gray-500">{metric.label}</span>
            <span className="text-sm font-medium text-gray-700">{metric.value}</span>
          </motion.div>
        ))}
      </div>

      {summaryMetric && (
        <div className="mt-4 pt-3 border-t border-gray-100">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-600">{summaryMetric.label}</span>
            <span className="text-sm font-bold text-gray-800">{summaryMetric.value}</span>
          </div>
        </div>
      )}
    </Card>
  );
};

export default MetricSection;
