import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import Card from '../ui/Card';

const BrandSalesChart = ({ brandData }) => {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState('30d');

  // Get the appropriate data based on the selected time range
  const getChartData = () => {
    if (!brandData || !brandData[timeRange]) {
      return [];
    }
    return brandData[timeRange];
  };

  // Get chart data with scaled units for better visualization
  const getScaledChartData = () => {
    const data = getChartData();
    // Create a scale factor to make units more visible (typically sales values are much larger than unit values)
    const scaleFactor = 150; // Adjust this value as needed

    return data.map(item => ({
      ...item,
      // Keep original values for tooltip but scale for display
      scaledUnits: item.units * scaleFactor
    }));
  };

  // Custom tooltip to show the breakdown
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      // Find the data for this brand to ensure we show all values
      const brandItem = getChartData().find(item => item.name === label);

      return (
        <div className="bg-white p-4 border rounded shadow-md">
          <p className="font-medium text-gray-900">{label}</p>
          <div className="mt-2">
            <p className="text-sm text-orange-600">
              <span className="font-medium">Sales:</span> AED {brandItem.sales.toLocaleString()}
            </p>
            <p className="text-sm text-red-600">
              <span className="font-medium">Units:</span> {brandItem.units.toLocaleString()}
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Card title={t('dashboard.charts.brandSales')}>
      <div className="mb-4 flex space-x-2">
        <button
          onClick={() => setTimeRange('1d')}
          className={`px-3 py-1 text-sm rounded-md ${
            timeRange === '1d'
              ? 'bg-indigo-100 text-indigo-700 font-medium'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          Daily
        </button>
        <button
          onClick={() => setTimeRange('7d')}
          className={`px-3 py-1 text-sm rounded-md ${
            timeRange === '7d'
              ? 'bg-indigo-100 text-indigo-700 font-medium'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          Weekly
        </button>
        <button
          onClick={() => setTimeRange('30d')}
          className={`px-3 py-1 text-sm rounded-md ${
            timeRange === '30d'
              ? 'bg-indigo-100 text-indigo-700 font-medium'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          Monthly
        </button>
      </div>

      <div className="h-96">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={getScaledChartData()}
            margin={{ top: 15, right: 10, left: 10, bottom: 10 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="name"
              type="category"
              tick={{ fontSize: 11 }}
            />
            <YAxis
              yAxisId="sales"
              type="number"
              orientation="left"
              tickFormatter={(value) => `AED ${value.toLocaleString()}`}
              stroke="#F59E0B"
            />
            <YAxis
              yAxisId="units"
              type="number"
              orientation="right"
              tickFormatter={(value) => Math.round(value / 150).toLocaleString()}
              stroke="#EF4444"
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend align='center' wrapperStyle={{ marginTop: '10px' }} />

            {/* Sales bars */}
            <Bar
              yAxisId="sales"
              dataKey="sales"
              name="Sales (AED)"
              fill="#F59E0B"
              radius={[4, 4, 0, 0]}
            />

            {/* Units bars */}
            <Bar
              yAxisId="units"
              dataKey="scaledUnits"
              name="Units"
              fill="#EF4444"
              radius={[4, 4, 0, 0]}
              barSize={20}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
};

export default BrandSalesChart;
