import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import Card from '../ui/Card';

const TaskPriorityCard = ({ tasks }) => {
  const { t } = useTranslation();

  // Calculate overall progress
  const calculateOverallProgress = () => {
    if (!tasks || tasks.length === 0) return 0;

    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(task => task.completed).length;
    const inProgressTasks = tasks.filter(task => !task.completed);

    let progressSum = 0;
    inProgressTasks.forEach(task => {
      progressSum += task.progress.current / task.progress.total;
    });

    const overallProgress = (completedTasks + progressSum) / totalTasks;
    return Math.round(overallProgress * 100);
  };

  const overallProgress = calculateOverallProgress();
  const remainingPercentage = 100 - overallProgress;

  // Get task icon
  const getTaskIcon = (iconType) => {
    switch (iconType) {
      case 'customer':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        );
      case 'vendor':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        );
      case 'product':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        );
      case 'promotion':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
          </svg>
        );
      case 'category':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        );
      case 'task':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
        );
      default:
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
        );
    }
  };

  // Get chevron right icon
  const ChevronRightIcon = () => (
    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
  );

  // Get check icon for completed tasks
  const CheckIcon = () => (
    <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );

  // Filter tasks by completion status
  const inProgressTasks = tasks ? tasks.filter(task => !task.completed) : [];
  const completedTasks = tasks ? tasks.filter(task => task.completed) : [];

  return (
    <Card title={t('dashboard.details.tasks')} fixedHeight={true}>
      <div className="space-y-1 h-full flex flex-col">
        {/* Progress Bar */}
        <div className="mb-1">
          <div className="flex justify-between items-center">
            <h3 className="font-bold text-gray-800">{remainingPercentage}{t('dashboard.details.remaining')}</h3>
          </div>
          <div className="h-0.5 bg-gray-200 rounded-full overflow-hidden mt-0.5">
            <div
              className={`h-full rounded-full ${
                overallProgress < 50 ? 'bg-red-500' :
                overallProgress < 100 ? 'bg-amber-500' : 'bg-green-500'
              }`}
              style={{ width: `${overallProgress}%` }}
            ></div>
          </div>
        </div>

        {/* Tasks Container - Scrollable */}
        <div className="overflow-y-auto flex-grow">
          {/* In Progress Tasks */}
          <div className="space-y-1">
            {inProgressTasks.length > 0 ? (
              <div className="space-y-1">
                {inProgressTasks.map((task) => (
                  <motion.div
                    key={task.id}
                    initial={{ opacity: 0, y: 3 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center justify-between p-1 border-b border-gray-100 hover:bg-gray-50 rounded cursor-pointer"
                  >
                    <div className="flex items-center space-x-1.5">
                        <div className="text-gray-600">
                          {getTaskIcon(task.icon)}
                        </div>
                        <div className="min-w-0">
                          <h4 className="font-medium text-gray-800 text-2xs truncate">{task.title}</h4>
                          <span className="text-3xs text-gray-500">
                            <div className="flex items-center space-x-2">
                              <span>{task.progress.current}/{task.progress.total}</span>
                              <div className="h-0.5 bg-gray-200 rounded-full w-16 overflow-hidden">
                              <div
                                className={`h-full ${
                                  task.completed ? 'bg-green-500' :
                                  (task.progress.current/task.progress.total) < 0.5 ? 'bg-red-500' : 'bg-amber-500'
                                }`}
                                style={{ width: `${task.progress.current/task.progress.total*100}%` }}
                              ></div>
                            </div>
                            <span className="text-gray-500 text-3xs">{Math.round((task.progress.current/task.progress.total)*100)}%</span>
                            <span>{task.icon === 'customer' ? t('dashboard.details.inquiries') : task.icon === 'product' ? t('dashboard.details.products') : t('dashboard.details.items')}</span>
                          </div>
                        </span>
                      </div>
                    </div>
                    <ChevronRightIcon />
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">{t('dashboard.details.noTasksInProgress')}</div>
            )}
          </div>

          {/* Completed Tasks */}
          {completedTasks.length > 0 && (
            <div>
              <h3 className="font-bold text-gray-700 text-xs mt-2 mb-0.5">{t('dashboard.details.completed')}</h3>
              <div className="space-y-1">
                {completedTasks.map((task) => (
                  <motion.div
                    key={task.id}
                    initial={{ opacity: 0, y: 3 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center justify-between p-1 border-b border-gray-100 hover:bg-gray-50 rounded cursor-pointer"
                  >
                    <div className="flex items-center space-x-2">
                      <div className="text-gray-600">
                        <CheckIcon />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-800 text-xs">{task.title}</h4>
                        <span className="text-2xs text-gray-500">
                          <div className="flex items-center space-x-0.5">
                            <span>{task.progress.current}/{task.progress.total}</span>
                            <div className="h-0.5 bg-gray-200 rounded-full w-16 overflow-hidden">
                              <div
                                className="h-full bg-green-500"
                                style={{ width: '100%' }}
                              ></div>
                            </div>
                            <span className="text-gray-500 text-3xs">100%</span>
                            <span>{task.icon === 'customer' ? t('dashboard.details.inquiries') : task.icon === 'product' ? t('dashboard.details.products') : t('dashboard.details.items')}</span>
                          </div>
                        </span>
                      </div>
                    </div>
                    <ChevronRightIcon />
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default TaskPriorityCard;
