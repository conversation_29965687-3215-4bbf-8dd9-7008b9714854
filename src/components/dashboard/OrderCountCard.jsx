import { useState } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import Card from '../ui/Card';

const OrderCountCard = ({ orderData, icon }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('24h');

  // Mock data for different time periods
  // In a real app, this would come from the API
  const timePeriodsData = {
    '24h': {
      total: Math.round(orderData.total * 0.15), // 15% of total for demo
      placed: Math.round(orderData.placed * 0.2),
      confirmed: Math.round(orderData.confirmed * 0.15),
      processed: Math.round(orderData.processed * 0.1),
      pending: Math.round(orderData.pending * 0.3)
    },
    '7d': {
      total: Math.round(orderData.total * 0.6), // 60% of total for demo
      placed: Math.round(orderData.placed * 0.7),
      confirmed: Math.round(orderData.confirmed * 0.6),
      processed: Math.round(orderData.processed * 0.5),
      pending: Math.round(orderData.pending * 0.8)
    },
    '30d': {
      total: orderData.total,
      placed: orderData.placed,
      confirmed: orderData.confirmed,
      processed: orderData.processed,
      pending: orderData.pending
    }
  };

  const currentData = timePeriodsData[activeTab];

  return (
    <Card title={t('dashboard.stats.orderCount')} fixedHeight={true}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex space-x-2">
          <button
            onClick={() => setActiveTab('24h')}
            className={`px-3 py-1 text-sm rounded-md ${
              activeTab === '24h'
                ? 'bg-purple-100 text-purple-700 font-medium'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            24 Hours
          </button>
          <button
            onClick={() => setActiveTab('7d')}
            className={`px-3 py-1 text-sm rounded-md ${
              activeTab === '7d'
                ? 'bg-purple-100 text-purple-700 font-medium'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            7 Days
          </button>
          <button
            onClick={() => setActiveTab('30d')}
            className={`px-3 py-1 text-sm rounded-md ${
              activeTab === '30d'
                ? 'bg-purple-100 text-purple-700 font-medium'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            30 Days
          </button>
        </div>
        <div className="p-3 rounded-full shadow-sm bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600">
          {icon}
        </div>
      </div>

      <div className="mt-2">
        <div className="text-3xl font-bold text-gray-800 mb-4">{currentData.total}</div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-amber-50 p-3 rounded-lg hover:bg-amber-100 transition-colors">
            <div className="text-sm text-amber-500">{t('dashboard.details.placed')}</div>
            <div className="text-lg font-semibold text-amber-800">{currentData.placed}</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg hover:bg-green-100 transition-colors">
            <div className="text-sm text-green-500">{t('dashboard.details.confirmed')}</div>
            <div className="text-lg font-semibold text-green-800">{currentData.confirmed}</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg hover:bg-green-100 transition-colors">
            <div className="text-sm text-green-500">{t('dashboard.details.processed')}</div>
            <div className="text-lg font-semibold text-green-800">{currentData.processed}</div>
          </div>
          <div className="bg-amber-50 p-3 rounded-lg hover:bg-amber-100 transition-colors">
            <div className="text-sm text-amber-500">{t('dashboard.details.pending')}</div>
            <div className="text-lg font-semibold text-amber-800">{currentData.pending}</div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default OrderCountCard;
