import React from 'react';

const TimePeriodFilter = ({ activePeriod, onChange }) => {
  return (
    <div className="flex space-x-2">
      <button
        onClick={() => onChange('1d')}
        className={`px-3 py-1 text-sm rounded-md ${
          activePeriod === '1d'
            ? 'bg-indigo-100 text-indigo-700 font-medium'
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
        }`}
      >
        24 Hours
      </button>
      <button
        onClick={() => onChange('7d')}
        className={`px-3 py-1 text-sm rounded-md ${
          activePeriod === '7d'
            ? 'bg-indigo-100 text-indigo-700 font-medium'
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
        }`}
      >
        7 Days
      </button>
      <button
        onClick={() => onChange('30d')}
        className={`px-3 py-1 text-sm rounded-md ${
          activePeriod === '30d'
            ? 'bg-indigo-100 text-indigo-700 font-medium'
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
        }`}
      >
        30 Days
      </button>
    </div>
  );
};

export default TimePeriodFilter;
