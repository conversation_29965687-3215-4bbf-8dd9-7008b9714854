:root {
  --tt-toolbar-height: 2.75rem;
  --tt-safe-area-bottom: env(safe-area-inset-bottom, 0px);
  --tt-toolbar-bg-color: var(--white);
  --tt-toolbar-border-color: var(--tt-gray-light-a-100);
}

.dark {
  --tt-toolbar-bg-color: var(--black);
  --tt-toolbar-border-color: var(--tt-gray-dark-a-50);
}

.tiptap-toolbar {
  display: flex;
  align-items: center;
  gap: 0.25rem;

  &-group {
    display: flex;
    align-items: center;
    gap: 0.125rem;

    &:empty {
      display: none;
    }

    &:empty + .tiptap-separator,
    .tiptap-separator + &:empty {
      display: none;
    }
  }

  &[data-variant="fixed"] {
    position: sticky;
    top: 0;
    z-index: 10;
    width: 100%;
    min-height: var(--tt-toolbar-height);
    background: var(--tt-toolbar-bg-color);
    border-bottom: 1px solid var(--tt-toolbar-border-color);
    padding: 0 0.5rem;
    overflow-x: auto;
    overscroll-behavior-x: contain;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    @media (max-width: 480px) {
      position: fixed;
      top: auto;
      bottom: 0;
      height: calc(var(--tt-toolbar-height) + var(--tt-safe-area-bottom));
      border-top: 1px solid var(--tt-toolbar-border-color);
      border-bottom: none;
      padding: 0 0.5rem var(--tt-safe-area-bottom);
      flex-wrap: nowrap;
      justify-content: flex-start;

      .tiptap-toolbar-group {
        flex: 0 0 auto;
      }
    }
  }

  &[data-variant="floating"] {
    --tt-toolbar-padding: 0.125rem;
    --tt-toolbar-border-width: 1px;

    padding: 0.188rem;
    border-radius: calc(
      var(--tt-toolbar-padding) + var(--tt-radius-lg) +
        var(--tt-toolbar-border-width)
    );
    border: var(--tt-toolbar-border-width) solid var(--tt-toolbar-border-color);
    background-color: var(--tt-toolbar-bg-color);
    box-shadow: var(--tt-shadow-elevated-md);
    outline: none;
    overflow: hidden;

    &[data-plain="true"] {
      padding: 0;
      border-radius: 0;
      border: none;
      box-shadow: none;
      background-color: transparent;
    }

    @media screen and (max-width: 768px) {
      width: 100%;
      border-radius: 0;
      border: none;
      box-shadow: none;
    }
  }
}
