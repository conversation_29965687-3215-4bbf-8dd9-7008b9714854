import { createSlice } from '@reduxjs/toolkit';
import { getCookie } from '../../utils/cookies';

// Get user from localStorage if available
const storedUser = localStorage.getItem('user');
const parsedUser = storedUser ? JSON.parse(storedUser) : null;

const initialState = {
  user: parsedUser ? {
    ...parsedUser,
    role: getCookie('role') || '',
    permissions: JSON.parse(getCookie('permissions')) || []
  } : null,
  token: getCookie('auth_token') || localStorage.getItem('token') || null,
  isAuthenticated: !!(getCookie('auth_token') || localStorage.getItem('token')),
  loading: false,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      state.loading = false;
      state.isAuthenticated = true;
      state.user = {
        ...action.payload.user,
        role: action.payload.role,
        permissions: action.payload.permissions
      };
      state.token = action.payload.token;
      // localStorage.setItem('token', JSON.stringify(action.payload.token));
      localStorage.setItem('user', JSON.stringify({
        ...action.payload.user,
        role: action.payload.role,
        permissions: action.payload.permissions
      }));
    },
    loginFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // Note: Cookie removal is handled in authService.logoutUser()
    },
    registerStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    registerSuccess: (state, action) => {
      state.loading = false;
      state.isAuthenticated = true;
      state.user = {
        ...action.payload.user,
        role: action.payload.user.role || 'viewer', // Default role for registered users
        permissions: action.payload.user.permissions || []
      };
      state.token = action.payload.token;
      localStorage.setItem('token', action.payload.token);
      localStorage.setItem('user', JSON.stringify({
        ...action.payload.user,
        role: action.payload.user.role || 'viewer',
        permissions: action.payload.user.permissions || []
      }));
    },
    registerFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  registerStart,
  registerSuccess,
  registerFailure,
  clearError,
} = authSlice.actions;

export default authSlice.reducer;
