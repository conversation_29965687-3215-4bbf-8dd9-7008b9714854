// // import { Formik, Form } from "formik";
// // import * as Yup from "yup";
// // import {
// //   FormInput,
// //   FormSelect,
// //   FormDatePicker,
// //   FormSwitch,
// // } from "@/components/ui/form";
// // import Button from "@/components/ui/Button";
// // import { useTranslation } from "react-i18next";
// // // import ProductVariantsTab from "@/components/ProductWizard/components/ProductVariantsTab";
// // import ProductVariantsTab from "../productComponent/VariantTab";

// // const mockAttributeList = [
// //   { id: 1, name: "Color", slug: "color" },
// //   { id: 2, name: "Size", slug: "size" },
// //   { id: 3, name: "Material", slug: "material" },
// // ];

// // const mockAttributeValueMap = {
// //   1: [
// //     { id: 1, product_attribute_id: 1, value: "Red" },
// //     { id: 2, product_attribute_id: 1, value: "Blue" },
// //     { id: 3, product_attribute_id: 1, value: "Green" },
// //     { id: 4, product_attribute_id: 1, value: "Black" },
// //     { id: 5, product_attribute_id: 1, value: "White" },
// //   ],
// //   2: [
// //     { id: 6, product_attribute_id: 2, value: "S" },
// //     { id: 7, product_attribute_id: 2, value: "M" },
// //     { id: 8, product_attribute_id: 2, value: "L" },
// //     { id: 9, product_attribute_id: 2, value: "XL" },
// //     { id: 10, product_attribute_id: 2, value: "XXL" },
// //   ],
// //   3: [
// //     { id: 11, product_attribute_id: 3, value: "Cotton" },
// //     { id: 12, product_attribute_id: 3, value: "Polyester" },
// //     { id: 13, product_attribute_id: 3, value: "Nylon" },
// //     { id: 14, product_attribute_id: 3, value: "Wool" },
// //   ],
// // };

// // const stockStatusOptions = [
// //   { label: "In Stock", value: "in_stock" },
// //   { label: "Out of Stock", value: "out_of_stock" },
// //   { label: "Low Stock", value: "low_stock" },
// // ];

// // const wareHouseOptions = [
// //   { label: "Main Warehouse", value: "main_warehouse" },
// //   { label: "Secondary Warehouse", value: "secondary_warehouse" },
// //   { label: "Distribution Center", value: "distribution_center" },
// //   { label: "Regional Hub", value: "regional_hub" },
// // ];

// // const StepFourForm = ({ dropdown, onSubmit, onCancel }) => {
// //   const { t } = useTranslation();

// //   const initialValues = {
// //     is_variant: false,
// //     variants: [],
// //     regular_price: "",
// //     offer_price: "",
// //     vat_tax: "",
// //     commission: "",
// //     discount_start: "",
// //     discount_end: "",
// //     current_stock: "",
// //     reserved_stock: "",
// //     low_stock_warning: "",
// //     stock_status: "",
// //     warehouse: "",
// //     ...dropdown,
// //   };

// //   const validationSchema = Yup.object({
// //     regular_price: Yup.number().when("is_variant", {
// //       is: false,
// //       then: Yup.number().required("Required"),
// //       otherwise: Yup.number(),
// //     }),
// //   });

// //   const handleSubmit = (values, { setSubmitting }) => {
// //     onSubmit(values);
// //     setSubmitting(false);
// //   };

// //   return (
// //     <Formik
// //       initialValues={initialValues}
// //       validationSchema={validationSchema}
// //       onSubmit={handleSubmit}
// //       enableReinitialize
// //     >
// //       {({ isSubmitting, values, setFieldValue }) => (
// //         <Form className="space-y-6">
// //           {/* Variant Switch */}
// //           <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
// //             <div className="flex items-center justify-center">
// //               <FormSwitch name="is_variant" label="Is Variant" />
// //             </div>
// //             <div className="flex-1">
// //               <p className="text-sm text-blue-700 mt-1">
// //                 Enable this if your product comes in different variations like
// //                 sizes, colors, or styles
// //               </p>
// //             </div>
// //             <div className="text-sm font-medium">
// //               Status:{" "}
// //               <span
// //                 className={`px-2 py-1 rounded transition ${
// //                   values.is_variant
// //                     ? "bg-green-100 text-green-800"
// //                     : "bg-gray-100 text-gray-800"
// //                 }`}
// //               >
// //                 {values.is_variant ? "Enabled" : " Disabled"}
// //               </span>
// //             </div>
// //           </div>

// //           {/* VARIANT MODE */}
// //           {values.is_variant ? (
// //             <ProductVariantsTab
// //               attributeList={mockAttributeList}
// //               attributeValueMap={mockAttributeValueMap}
// //               variants={values.variants || []}
// //               onChange={(variants) => setFieldValue("variants", variants)}
// //               productId={dropdown?.system_sku || "PROD"}
// //               defaultRegularPrice={values.regular_price || 0}
// //               defaultOfferPrice={values.offer_price || 0}
// //             />
// //           ) : (
// //             <>
// //               {/* SINGLE PRODUCT PRICING */}
// //               <div className="bg-white p-6 shadow border border-red-400 rounded-md">
// //                 <span className="text-lg font-medium my-3">
// //                   Pricing Information
// //                 </span>
// //                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
// //                   <FormInput
// //                     name="regular_price"
// //                     label="Regular Price (SAR)"
// //                     placeholder={t("commonPlaceholder.ProductTitle")}
// //                     required
// //                   />
// //                   <FormInput
// //                     name="offer_price"
// //                     label="Offer Price (SAR)"
// //                     placeholder={t("commonPlaceholder.name_enPlaceholder")}
// //                   />
// //                   <FormInput
// //                     name="vat_tax"
// //                     label="VAT Tax (%)"
// //                     placeholder={t("commonPlaceholder.name_enPlaceholder")}
// //                   />
// //                   <FormInput
// //                     name="approx_commission"
// //                     label="Approx. Commission (%)"
// //                     placeholder={t("commonPlaceholder.name_enPlaceholder")}
// //                   />
// //                   <FormDatePicker
// //                     name="discount_start_date"
// //                     label="Discount Start Date"
// //                   />
// //                   <FormDatePicker
// //                     name="discount_end_date"
// //                     label="Discount End Date"
// //                   />
// //                 </div>
// //               </div>
// //               {/* SINGLE PRODUCT INVENTORY */}
// //               {/* <div className="bg-white p-6 shadow border border-red-400 rounded-md">
// //                 <span className="text-lg font-medium my-3">
// //                   Inventory Management
// //                 </span>
// //                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
// //                   <FormInput
// //                     name="current_stock"
// //                     label="Current Stock"
// //                     placeholder={t("commonPlaceholder.name_enPlaceholder")}
// //                   />
// //                   <FormInput
// //                     name="reserved_stock"
// //                     label="Reserved for Orders"
// //                     placeholder={t("commonPlaceholder.name_enPlaceholder")}
// //                   />
// //                   <FormInput
// //                     name="low_stock_warning"
// //                     label="Low-stock Warning Level"
// //                     placeholder={t("commonPlaceholder.name_enPlaceholder")}
// //                   />
// //                   <FormSelect
// //                     name="stock_status"
// //                     label="Stock Status"
// //                     options={stockStatusOptions}
// //                   />
// //                   <div className="col-span-2">
// //                     <FormSelect
// //                       name="warehouse"
// //                       label="Warehouse"
// //                       options={wareHouseOptions}
// //                     />
// //                   </div>
// //                 </div>
// //               </div> */}
// //             </>
// //           )}

// //           {/* Action Buttons */}
// //           <div className="flex justify-end pt-6 border-t border-gray-200 mt-8 gap-3">
// //             <Button type="button" variant="outline" onClick={onCancel}>
// //               {t("commonButton.cancel")}
// //             </Button>
// //             <Button
// //               type="submit"
// //               variant="primary"
// //               loading={isSubmitting}
// //               disabled={isSubmitting}
// //             >
// //               {dropdown
// //                 ? t("commonButton.dropDown.updated")
// //                 : t("commonButton.dropDown.create")}
// //             </Button>
// //           </div>
// //         </Form>
// //       )}
// //     </Formik>
// //   );
// // };

// // export default StepFourForm;

// import { Formik, Form } from "formik";
// import * as Yup from "yup";
// import {
//   FormInput,
//   FormSelect,
//   FormDatePicker,
//   FormSwitch,
// } from "@/components/ui/form";
// import Button from "@/components/ui/Button";
// import { useTranslation } from "react-i18next";
// import ProductVariantsTab from "../productComponent/VariantTab";

// const mockAttributeList = [
//   { id: 1, name: "Color", slug: "color" },
//   { id: 2, name: "Size", slug: "size" },
//   { id: 3, name: "Material", slug: "material" },
// ];

// const mockAttributeValueMap = {
//   1: [
//     { id: 1, product_attribute_id: 1, value: "Red" },
//     { id: 2, product_attribute_id: 1, value: "Blue" },
//     { id: 3, product_attribute_id: 1, value: "Green" },
//     { id: 4, product_attribute_id: 1, value: "Black" },
//     { id: 5, product_attribute_id: 1, value: "White" },
//   ],
//   2: [
//     { id: 6, product_attribute_id: 2, value: "S" },
//     { id: 7, product_attribute_id: 2, value: "M" },
//     { id: 8, product_attribute_id: 2, value: "L" },
//     { id: 9, product_attribute_id: 2, value: "XL" },
//     { id: 10, product_attribute_id: 2, value: "XXL" },
//   ],
//   3: [
//     { id: 11, product_attribute_id: 3, value: "Cotton" },
//     { id: 12, product_attribute_id: 3, value: "Polyester" },
//     { id: 13, product_attribute_id: 3, value: "Nylon" },
//     { id: 14, product_attribute_id: 3, value: "Wool" },
//   ],
// };

// const stockStatusOptions = [
//   { label: "In Stock", value: "in_stock" },
//   { label: "Out of Stock", value: "out_of_stock" },
//   { label: "Low Stock", value: "low_stock" },
// ];

// const wareHouseOptions = [
//   { label: "Main Warehouse", value: "main_warehouse" },
//   { label: "Secondary Warehouse", value: "secondary_warehouse" },
//   { label: "Distribution Center", value: "distribution_center" },
//   { label: "Regional Hub", value: "regional_hub" },
// ];

// const vatTaxOptions = [
//   { label: "Standard 5%", value: "standard_5" },
//   { label: "Zero 0%", value: "zero_0" },
//   { label: "Exempt", value: "exempt" },
// ];

// const StepFourForm = ({ dropdown, onSubmit, onCancel }) => {
//   const { t } = useTranslation();

//   const initialValues = {
//     is_variant: false,
//     variants: [],
//     regular_price: "",
//     offer_price: "",
//     vat_tax: "",
//     approx_commission: "",
//     discount_start_date: "",
//     discount_end_date: "",
//     current_stock: "",
//     reserved_stock: "",
//     low_stock_warning: "",
//     stock_status: "",
//     warehouse: "",
//     ...dropdown,
//   };

//   const validationSchema = Yup.object({
//     regular_price: Yup.number().when("is_variant", {
//       is: false,
//       then: Yup.number().required("Required"),
//       otherwise: Yup.number(),
//     }),
//     vat_tax: Yup.string().required("VAT Tax is required"),
//     approx_commission: Yup.number().nullable(),
//     discount_start_date: Yup.date().nullable(),
//     discount_end_date: Yup.date().nullable(),
//   });

//   const handleSubmit = (values, { setSubmitting }) => {
//     // Convert string numbers to actual numbers if needed
//     const payload = {
//       regular_price: parseFloat(values.regular_price),
//       offer_price: values.offer_price
//         ? parseFloat(values.offer_price)
//         : undefined,
//       vat_tax: values.vat_tax,
//       discount_start_date: values.discount_start_date,
//       discount_end_date: values.discount_end_date,
//       approx_commission: values.approx_commission
//         ? parseFloat(values.approx_commission)
//         : undefined,
//       // ...add the rest if needed
//     };
//     onSubmit(payload);
//     setSubmitting(false);
//   };

//   return (
//     <Formik
//       initialValues={initialValues}
//       validationSchema={validationSchema}
//       onSubmit={handleSubmit}
//       enableReinitialize
//     >
//       {({ isSubmitting, values, setFieldValue }) => (
//         <Form className="space-y-6">
//           <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
//             <div className="flex items-center justify-center">
//               <FormSwitch name="is_variant" label="Is Variant" />
//             </div>
//             <div className="flex-1">
//               <p className="text-sm text-blue-700 mt-1">
//                 Enable this if your product comes in different variations like
//                 sizes, colors, or styles
//               </p>
//             </div>
//             <div className="text-sm font-medium">
//               Status:{" "}
//               <span
//                 className={`px-2 py-1 rounded transition ${
//                   values.is_variant
//                     ? "bg-green-100 text-green-800"
//                     : "bg-gray-100 text-gray-800"
//                 }`}
//               >
//                 {values.is_variant ? "Enabled" : " Disabled"}
//               </span>
//             </div>
//           </div>

//           {values.is_variant ? (
//             <ProductVariantsTab
//               attributeList={mockAttributeList}
//               attributeValueMap={mockAttributeValueMap}
//               variants={values.variants || []}
//               onChange={(variants) => setFieldValue("variants", variants)}
//               productId={dropdown?.system_sku || "PROD"}
//               defaultRegularPrice={values.regular_price || 0}
//               defaultOfferPrice={values.offer_price || 0}
//             />
//           ) : (
//             <>
//               <div className="bg-white p-6 shadow border border-red-400 rounded-md">
//                 <span className="text-lg font-medium my-3">
//                   Pricing Information
//                 </span>
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
// <FormInput
//   name="regular_price"
//   label="Regular Price (SAR)"
//   type="number"
//   step="0.01"
//   required
// />
// <FormInput
//   name="offer_price"
//   label="Offer Price (SAR)"
//   type="number"
//   step="0.01"
// />
// <FormSelect
//   name="vat_tax"
//   label="VAT Tax"
//   options={vatTaxOptions}
//   required
// />
// <FormInput
//   name="approx_commission"
//   label="Approx. Commission (%)"
//   type="number"
//   step="0.01"
// />
// <FormDatePicker
//   name="discount_start_date"
//   label="Discount Start Date"
// />
// <FormDatePicker
//   name="discount_end_date"
//   label="Discount End Date"
// />
//                 </div>
//               </div>
//             </>
//           )}

//           <div className="flex justify-end pt-6 border-t border-gray-200 mt-8 gap-3">
//             <Button type="button" variant="outline" onClick={onCancel}>
//               {t("commonButton.cancel")}
//             </Button>
//             <Button
//               type="submit"
//               variant="primary"
//               loading={isSubmitting}
//               disabled={isSubmitting}
//             >
//               {dropdown
//                 ? t("commonButton.dropDown.updated")
//                 : t("commonButton.dropDown.create")}
//             </Button>
//           </div>
//         </Form>
//       )}
//     </Formik>
//   );
// };

// export default StepFourForm;

import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormInput, FormSelect, FormDatePicker } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";

const vatTaxOptions = [
  { label: "Standard 5%", value: "standard_5" },
  { label: "Zero 0%", value: "zero_0" },
  { label: "Exempt", value: "exempt" },
];

const StepFourForm = ({ dropdown, onNext, onCancel, onBack }) => {
  const { t } = useTranslation();

  const initialValues = dropdown
    ? {
        id: dropdown.id,
        regular_price: dropdown.regular_price || "",
        offer_price: dropdown.offer_price || "",
        vat_tax: dropdown.vat_tax || "",
        approx_commission: dropdown.approx_commission || "",
        discount_start_date: dropdown.discount_start_date || "",
        discount_end_date: dropdown.discount_end_date || "",
      }
    : {
        regular_price: "",
        offer_price: "",
        vat_tax: "",
        approx_commission: "",
        discount_start_date: "",
        discount_end_date: "",
      };

  const validationSchema = Yup.object({
    regular_price: Yup.string().required(t("commonValidation.title_required")),
  });

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={(values, { setSubmitting }) => {
        onNext(values);
        setSubmitting(false);
      }}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => {
        return (
          <Form className="space-y-6">
            <div className="bg-white p-6 shadow border border-red-400 rounded-md">
              <span className="text-lg font-medium my-3">
                Pricing Information
              </span>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                <FormInput
                  name="regular_price"
                  label="Regular Price (SAR)"
                  type="number"
                  step="0.01"
                  required
                />
                <FormInput
                  name="offer_price"
                  label="Offer Price (SAR)"
                  type="number"
                  step="0.01"
                />
                <FormSelect
                  name="vat_tax"
                  label="VAT Tax"
                  options={vatTaxOptions}
                  required
                />
                <FormInput
                  name="approx_commission"
                  label="Approx. Commission (%)"
                  type="number"
                  step="0.01"
                />
                <FormDatePicker
                  name="discount_start_date"
                  label="Discount Start Date"
                />
                <FormDatePicker
                  name="discount_end_date"
                  label="Discount End Date"
                />
              </div>
            </div>

            <div className="flex justify-end pt-6 border-t border-gray-200 mt-8 gap-3">
              <Button type="button" variant="outline" onClick={onCancel}>
                {t("commonButton.cancel")}
              </Button>
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack}>
                  {t("commonButton.previous") || "Previous"}
                </Button>
              )}
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {dropdown
                  ? t("commonButton.dropDown.updated")
                  : t("commonButton.dropDown.create")}
              </Button>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default StepFourForm;
