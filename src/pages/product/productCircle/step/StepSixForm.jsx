import { Formik, Form } from "formik";
import * as Yup from "yup";
import {
  FormInput,
  FormSelect,
  FormTextarea,
  FormSwitch,
  FormDatePicker,
} from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";

const dietOptions = [
  { label: "Vegan", value: "vegan" },
  { label: "Gluten Free", value: "gluten_free" },
  { label: "Sugar Free", value: "sugar_free" },
  { label: "Dairy Free", value: "dairy_free" },
  { label: "Nut Free", value: "nut_free" },
  { label: "Soy Free", value: "soy_free" },
  { label: "Organic", value: "organic" },
  { label: "Non-GMO", value: "non_gmo" },
  { label: "Keto", value: "keto" },
  { label: "Paleo", value: "paleo" },
  { label: "Low Carb", value: "low_carb" },
  { label: "High Protein", value: "high_protein" },
];

const vatRateOptions = [
  { label: "Standard Rate (15%)", value: "standard_15" },
  { label: "Zero Rate (0%)", value: "zero_0" },
  { label: "Exempt", value: "exempt" },
  { label: "Reduced Rate (5%)", value: "reduced_5" },
];
const fulfillmentOptions = [
  { label: "FBA", value: "fba" },
  { label: "Drop Ship", value: "drop_ship" },
  { label: "Self Ship", value: "self_ship" },
];

const collectionOptions = [
  { label: "Main Warehouse", value: "main_warehouse" },
  { label: "Secondary Warehouse", value: "secondary_warehouse" },
  { label: "Distribution Center", value: "distribution_center" },
  { label: "Pickup Point 1", value: "pickup_point_1" },
  { label: "Pickup Point 2", value: "pickup_point_2" },
];

const StepSixForm = ({ dropdown, onSubmit, onCancel }) => {
  const { t } = useTranslation();

  const initialValues = dropdown
    ? {
        id: dropdown.id,
        category_id: dropdown.category_id || "",
        sub_category_id: dropdown.sub_category_id || "",
        class_id: dropdown.class_id || "",
        sub_class_id: dropdown.sub_class_id || "",
        brand_id: dropdown.brand_id || "",
        vendor_sku: dropdown.vendor_sku || "",
        barcode: dropdown.barcode || "",
        model_number: dropdown.model_number || "",
      }
    : {
        category_id: "",
        sub_category_id: "",
        class_id: "",
        sub_class_id: "",
        brand_id: "",
        vendor_sku: "",
        barcode: "",
        model_number: "",
      };

  const validationSchema = Yup.object({
    category_id: Yup.string().required(t("commonValidation.category_required")),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    onSubmit(values);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => {
        return (
          <Form className="space-y-6">
            <div className="bg-white p-6 shadow border border-red-400 rounded-md">
              <span className="text-lg font-medium my-3">SEO & Metadata</span>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                <FormInput
                  name="vendor_sku"
                  label="Meta Title (English)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormInput
                  name="vendor_sku"
                  label="Meta Title (العربية)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormTextarea
                  name="summary_en"
                  label="Meta Description (English)"
                  placeholder="Enter Summary in English"
                />
                <FormTextarea
                  name="summary_en"
                  label="Meta Description (العربية)"
                  placeholder="Enter Summary in English"
                />
                <FormInput
                  name="vendor_sku"
                  label="Keywords (English)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormInput
                  name="vendor_sku"
                  label="Keywords (English)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <div className="col-span-2">
                  <FormInput
                    name="vendor_sku"
                    label="URL Slug"
                    placeholder={t("commonPlaceholder.ProductTitle")}
                    required
                  />
                </div>
              </div>
            </div>

            <div className="bg-white p-6 shadow border border-red-400 rounded-md">
              <span className="text-lg font-medium my-3">
                Frequently Asked Questions
              </span>
              <div className="bg-cyan-50 p-3 rounded-lg border border-cyan-200 my-2">
                <p className="text-cyan-600 text-sm">
                  Add common questions and answers about your product in both
                  languages
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                <FormInput
                  name="vendor_sku"
                  label="Question (En)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormInput
                  name="vendor_sku"
                  label="Question (Ar)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormTextarea
                  name="summary_en"
                  label="Answer (En)"
                  placeholder="Enter Summary in English"
                />
                <FormTextarea
                  name="summary_en"
                  label="Answer (Ar)"
                  placeholder="Enter Summary in English"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end pt-6 border-t border-gray-200 mt-8 gap-3">
              <Button type="button" variant="outline" onClick={onCancel}>
                {t("commonButton.cancel")}
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {dropdown
                  ? t("commonButton.dropDown.updated")
                  : t("commonButton.dropDown.create")}
              </Button>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default StepSixForm;
