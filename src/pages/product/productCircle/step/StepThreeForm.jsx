import { Formik, Form } from "formik";
import * as Yup from "yup";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import ImageUpload from "@/components/ui/ImageUpload";

const StepThreeForm = ({
  dropdown,
  onNext,
  onCancel,
  onBack,
  onNextStep,
  productId,
  onMediaUploaded,
}) => {
  const { t } = useTranslation();

  const initialValues = dropdown
    ? {
        id: dropdown.id,
        title_en: dropdown.title_en || "",
      }
    : {
        title_en: "",
      };

  const validationSchema = Yup.object({
    title_en: Yup.string().required(t("commonValidation.title_required")),
  });

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={(values, { setSubmitting }) => {
        onNext(values);
        setSubmitting(false);
      }}
      enableReinitialize
    >
      {({ isSubmitting, dirty, setFieldValue }) => (
        <Form className="space-y-6">
          <div className="bg-white p-6 shadow border border-red-400 rounded-md">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Primary Images
            </label>
            <ImageUpload
              value={initialValues.featured_image}
              onUploadSuccess={(url) => setFieldValue("featured_image", url)}
            />
          </div>
          <div className="flex justify-end pt-6 border-t border-gray-200 mt-8 gap-3">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("commonButton.cancel")}
            </Button>
            {onBack && (
              <Button type="button" variant="outline" onClick={onBack}>
                {t("previous") || "Previous"}
              </Button>
            )}
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {dropdown
                ? t("commonButton.dropDown.updated")
                : t("commonButton.dropDown.create")}
            </Button>
            {dropdown && !dirty && (
              <Button type="button" variant="secondary" onClick={onNextStep}>
                {t("Next") || "Next"}
              </Button>
            )}
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default StepThreeForm;
