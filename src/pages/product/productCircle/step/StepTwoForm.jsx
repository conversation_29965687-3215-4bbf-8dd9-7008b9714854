import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormInput, FormSelect, FormTextarea } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";

const userGroupOptions = [
  { label: "Men", value: "men" },
  { label: "Women", value: "women" },
  { label: "Unisex Gender Neutral", value: "unisex" },
  { label: "Senior Adults", value: "senior_adults" },
  { label: "Kids", value: "kids" },
  { label: "Teens", value: "teens" },
];
const netWeightUnitOptions = [
  { label: "Kilograms (kg)", value: "kg" },
  { label: "Grams (g)", value: "g" },
  { label: "Milligrams (mg)", value: "mg" },
  { label: "Liters (L)", value: "l" },
  { label: "Milliliters (ml)", value: "ml" },
  { label: "Pieces", value: "pieces" },
  { label: "Centimeters (cm)", value: "cm" },
  { label: "Meters (m)", value: "m" },
  { label: "Inches", value: "inches" },
];
const formulationOptions = [
  { label: "Tablet", value: "tablet" },
  { label: "Capsule", value: "capsule" },
  { label: "Powder", value: "powder" },
  { label: "Liquid", value: "liquid" },
  { label: "Cream", value: "cream" },
  { label: "Gel", value: "gel" },
  { label: "Spray", value: "spray" },
  { label: "Drops", value: "drops" },
  { label: "Syrup", value: "syrup" },
  { label: "Other", value: "other" },
];
const flavorOptions = [
  { label: "Vanilla", value: "vanilla" },
  { label: "Chocolate", value: "chocolate" },
  { label: "Strawberry", value: "strawberry" },
  { label: "Banana", value: "banana" },
  { label: "Orange", value: "orange" },
  { label: "Lemon", value: "lemon" },
  { label: "Mint", value: "mint" },
  { label: "Berry", value: "berry" },
  { label: "Unflavored", value: "unflavored" },
  { label: "Other", value: "other" },
];

const StepTwoForm = ({ dropdown, onNext, onCancel, onBack, onNextStep }) => {
  const { t } = useTranslation();

  const initialValues = dropdown
    ? {
        id: dropdown.id,
        title_en: dropdown.title_en || "",
        title_ar: dropdown.title_ar || "",
        short_name: dropdown.short_name || "",
        short_description_en: dropdown.short_description_en || "",
        short_description_ar: dropdown.short_description_ar || "",
        description_en: dropdown.description_en || "",
        description_ar: dropdown.description_ar || "",
        key_ingredients: dropdown.key_ingredients || "",
        usage_instructions: dropdown.usage_instructions || "",
        user_group: dropdown.user_group || "",
        net_weight_unit: dropdown.net_weight_unit || "",
        net_weight: dropdown.net_weight || "",
        formulation: dropdown.formulation || "",
        flavour: dropdown.flavour || "",
        servings: dropdown.servings || "",
      }
    : {
        title_en: "",
        title_ar: "",
        short_name: "",
        short_description_en: "",
        short_description_ar: "",
        description_en: "",
        description_ar: "",
        key_ingredients: "",
        usage_instructions: "",
        user_group: "",
        net_weight_unit: "",
        net_weight: "",
        formulation: "",
        flavour: "",
        servings: "",
      };

  const validationSchema = Yup.object({
    title_en: Yup.string().required(t("commonValidation.title_required")),
    title_ar: Yup.string().required(t("commonValidation.title_required")),
  });

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={(values, { setSubmitting }) => {
        onNext(values);
        setSubmitting(false);
      }}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values, dirty }) => {
        return (
          <Form className="space-y-6">
            <div className="bg-white p-6 shadow border border-red-400 rounded-md">
              <span className="text-lg font-medium my-3">
                Basic Information
              </span>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                <FormInput
                  name="title_en"
                  label="Product Title (English)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormInput
                  name="title_ar"
                  label="Product Title"
                  placeholder={t("commonPlaceholder.name_enPlaceholder")}
                  required
                />
                <span className="col-span-2">
                  <FormInput
                    name="short_name"
                    label="Short Name"
                    placeholder={t("commonPlaceholder.name_enPlaceholder")}
                    required
                  />
                </span>
              </div>
            </div>
            <div className="bg-white p-6 shadow border border-red-400 rounded-md">
              <span className="text-lg font-medium my-3">
                Product Descriptions
              </span>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                <FormTextarea
                  name="short_description_en"
                  label="Short Description (En)"
                  placeholder="Enter Summary in English"
                />
                <FormTextarea
                  name="short_description_ar"
                  label="Short Description (Ar)"
                  placeholder="Enter Summary in Arabic"
                />
                <FormTextarea
                  name="description_en"
                  label="Full Description (En)"
                  placeholder="Enter Summary in English"
                />
                <FormTextarea
                  name="description_ar"
                  label="Full Description (Ar)"
                  placeholder="Enter Summary in Arabic"
                />
              </div>
            </div>
            <div className="bg-white p-6 shadow border border-red-400 rounded-md">
              <span className="text-lg font-medium my-3">
                Additional Product Information
              </span>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                <FormTextarea
                  name="key_ingredients"
                  label="Key Ingredients (En)"
                  placeholder="Enter Summary in English"
                />
                <FormTextarea
                  name="usage_instructions"
                  label="Usage Instructions (En)"
                  placeholder="Enter Summary in English"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
                <FormSelect
                  name="user_group"
                  label="User Group"
                  options={userGroupOptions}
                  required
                />
                <FormSelect
                  name="net_weight_unit"
                  label="Net Weight Unit"
                  options={netWeightUnitOptions}
                  required
                />
                <FormInput
                  name="net_weight"
                  label="Net Weight"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormSelect
                  name="formulation"
                  label="Formulation"
                  options={formulationOptions}
                  required
                />
                <FormSelect
                  name="flavour"
                  label="Flavour"
                  options={flavorOptions}
                  required
                />
                <FormInput
                  name="servings"
                  label="Servings"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
              </div>
            </div>
            <div className="flex justify-end pt-6 border-t border-gray-200 mt-8 gap-3">
              <Button type="button" variant="outline" onClick={onCancel}>
                {t("commonButton.cancel")}
              </Button>
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack}>
                  {t("previous") || "Previous"}
                </Button>
              )}
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {dropdown
                  ? t("commonButton.dropDown.updated")
                  : t("commonButton.dropDown.create")}
              </Button>
              {dropdown && !dirty && (
                <Button type="button" variant="secondary" onClick={onNextStep}>
                  {t("Next") || "Next"}
                </Button>
              )}
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default StepTwoForm;
