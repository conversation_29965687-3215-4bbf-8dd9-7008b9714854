import { Formik, Form } from "formik";
import * as Yup from "yup";
import {
  FormInput,
  FormSelect,
  FormTextarea,
  FormSwitch,
  FormDatePicker,
} from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";

const dietOptions = [
  { label: "Vegan", value: "vegan" },
  { label: "Gluten Free", value: "gluten_free" },
  { label: "Sugar Free", value: "sugar_free" },
  { label: "Dairy Free", value: "dairy_free" },
  { label: "Nut Free", value: "nut_free" },
  { label: "Soy Free", value: "soy_free" },
  { label: "Organic", value: "organic" },
  { label: "Non-GMO", value: "non_gmo" },
  { label: "Keto", value: "keto" },
  { label: "Paleo", value: "paleo" },
  { label: "Low Carb", value: "low_carb" },
  { label: "High Protein", value: "high_protein" },
];

const vatRateOptions = [
  { label: "Standard Rate (15%)", value: "standard_15" },
  { label: "Zero Rate (0%)", value: "zero_0" },
  { label: "Exempt", value: "exempt" },
  { label: "Reduced Rate (5%)", value: "reduced_5" },
];
const fulfillmentOptions = [
  { label: "FBA", value: "fba" },
  { label: "Drop Ship", value: "drop_ship" },
  { label: "Self Ship", value: "self_ship" },
];

const collectionOptions = [
  { label: "Main Warehouse", value: "main_warehouse" },
  { label: "Secondary Warehouse", value: "secondary_warehouse" },
  { label: "Distribution Center", value: "distribution_center" },
  { label: "Pickup Point 1", value: "pickup_point_1" },
  { label: "Pickup Point 2", value: "pickup_point_2" },
];

const StepFiveForm = ({ dropdown, onSubmit, onCancel }) => {
  const { t } = useTranslation();

  const initialValues = dropdown
    ? {
        id: dropdown.id,
        category_id: dropdown.category_id || "",
        sub_category_id: dropdown.sub_category_id || "",
        class_id: dropdown.class_id || "",
        sub_class_id: dropdown.sub_class_id || "",
        brand_id: dropdown.brand_id || "",
        vendor_sku: dropdown.vendor_sku || "",
        barcode: dropdown.barcode || "",
        model_number: dropdown.model_number || "",
      }
    : {
        category_id: "",
        sub_category_id: "",
        class_id: "",
        sub_class_id: "",
        brand_id: "",
        vendor_sku: "",
        barcode: "",
        model_number: "",
      };

  const validationSchema = Yup.object({
    category_id: Yup.string().required(t("commonValidation.category_required")),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    onSubmit(values);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => {
        return (
          <Form className="space-y-6">
            <div className="bg-white p-6 shadow border border-red-400 rounded-md">
              <span className="text-lg font-medium my-3">
                Product Compliance
              </span>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                <div className="col-span-2">
                  <FormSelect
                    name="user_group"
                    label="Dietary Needs"
                    options={dietOptions}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-1 gap-4 p-4">
                <FormTextarea
                  name="summary_en"
                  label="Allergen Information"
                  placeholder="Enter Summary in English"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
                <FormSwitch name="is_vegan" label="Vegan" />
                <FormSwitch name="is_vegetarian" label="Vegetarian" />
                <FormSwitch name="is_gluten_free" label="Halal" />
              </div>
            </div>

            <div className="bg-white p-6 shadow border border-red-400 rounded-md">
              <span className="text-lg font-medium my-3">Storage & Origin</span>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                <div className="col-span-2">
                  <FormInput
                    name="vendor_sku"
                    label="Storage Conditions"
                    placeholder={t("commonPlaceholder.ProductTitle")}
                    required
                  />
                </div>
                <FormSelect
                  name="user_group"
                  label="VAT Tax UTL"
                  options={vatRateOptions}
                  required
                />
                <FormInput
                  name="vendor_sku"
                  label="Product Registration No"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormInput
                  name="vendor_sku"
                  label="Country of Origin"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormDatePicker
                  name="expiry_date"
                  label="Best Before Date"
                  required
                />
              </div>
            </div>

            <div className="bg-white p-6 shadow border border-red-400 rounded-md">
              <span className="text-lg font-medium my-3">
                Fulfillment Method
              </span>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                <FormSelect
                  name="user_group"
                  label="Fulfillment Mode"
                  options={fulfillmentOptions}
                  required
                />
                <FormSelect
                  name="user_group"
                  label="Collection Point"
                  options={collectionOptions}
                  required
                />
                <FormSwitch name="is_vegan" label="Returnable" />
              </div>
            </div>
            <div className="bg-white p-6 shadow border border-red-400 rounded-md">
              <span className="text-lg font-medium my-3">
                Shipping Information
              </span>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                <FormInput
                  name="vendor_sku"
                  label="Shipping Time"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormInput
                  name="vendor_sku"
                  label="Shipping Fee (SAR)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4">
                <FormInput
                  name="vendor_sku"
                  label="Length (cm)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormInput
                  name="vendor_sku"
                  label="Width (cm)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormInput
                  name="vendor_sku"
                  label="Height (cm)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
                <FormInput
                  name="vendor_sku"
                  label="Weight (kg)"
                  placeholder={t("commonPlaceholder.ProductTitle")}
                  required
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end pt-6 border-t border-gray-200 mt-8 gap-3">
              <Button type="button" variant="outline" onClick={onCancel}>
                {t("commonButton.cancel")}
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {dropdown
                  ? t("commonButton.dropDown.updated")
                  : t("commonButton.dropDown.create")}
              </Button>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default StepFiveForm;
