import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { toast } from "sonner";
import DropDownFormStep1 from "./step/StepOneForm";
import DropDownFormStep2 from "./step/StepTwoForm";
import DropDownFormStep3 from "./step/StepThreeForm";
import DropDownFormStep4 from "./step/StepFourForm";
// import DropDownFormStep3 from "./step/StepThreeForm";
// ...import the rest of your steps
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";
import Stepper from "./Stepper";

const steps = [
  { label: "Classification" },
  { label: "Product Details" },
  { label: "Product Media" },
  { label: "Pricing & Inventory" },
  { label: "Compliance & Fulfillment" },
  { label: "SEO & FAQs" },
];

const ProductCircleAddEditPage = () => {
  const { id: urlId } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({});
  const [productId, setProductId] = useState(urlId || null);
  const { fetchData, postMutation, putMutation } = useApi();

  const { data, isLoading, isError } = fetchData(
    `admin/products/${productId}`,
    undefined,
    {
      enabled: !!urlId,
    }
  );
  const dropDownData = data?.data;

  // POST (step 1)
  // const handleStepOneNext = (values) => {
  //   setLoading(true);
  //   postMutation.mutate(
  //     { endpoint: "admin/products", data: values },
  //     {
  //       onSuccess: (response) => {
  //         const newId =
  //           response?.data?.id || response?.id || response?.data?.data?.id;
  //         setProductId(newId);
  //         setActiveStep((prev) => prev + 1);
  //         toast.success(t("commonToast.dropDownToast.dropDownCreate"));
  //       },
  //       onError: (error) => {
  //         toast.error(
  //           "Product creation failed: " +
  //             (error?.response?.data?.message || error.message)
  //         );
  //       },
  //       onSettled: () => setLoading(false),
  //     }
  //   );
  // };

  const handleStepOneNext = (values) => {
    setLoading(true);
    // যদি প্রোডাক্ট আইডি থাকে (এডিট মোড), তাহলে PUT
    if (productId) {
      putMutation.mutate(
        { endpoint: `admin/products/${productId}`, data: values },
        {
          onSuccess: () => {
            setActiveStep((prev) => prev + 1);
            toast.success(t("commonToast.dropDownToast.dropDownUpdate"));
          },
          onError: (error) => {
            toast.error(
              "Product update failed: " +
                (error?.response?.data?.message || error.message)
            );
          },
          onSettled: () => setLoading(false),
        }
      );
    } else {
      // না থাকলে নতুন প্রোডাক্ট POST
      postMutation.mutate(
        { endpoint: "admin/products", data: values },
        {
          onSuccess: (response) => {
            const newId =
              response?.data?.id || response?.id || response?.data?.data?.id;
            setProductId(newId);
            setActiveStep((prev) => prev + 1);
            toast.success(t("commonToast.dropDownToast.dropDownCreate"));
          },
          onError: (error) => {
            toast.error(
              "Product creation failed: " +
                (error?.response?.data?.message || error.message)
            );
          },
          onSettled: () => setLoading(false),
        }
      );
    }
  };

  // PUT (steps 2-7)
  const handleStepNext = (values) => {
    setLoading(true);
    putMutation.mutate(
      {
        endpoint: `admin/products/${productId}`,
        data: values,
      },
      {
        onSuccess: () => {
          setActiveStep((prev) => prev + 1);
          toast.success(t("commonToast.dropDownToast.dropDownUpdate"));
        },
        onError: (error) => {
          toast.error(
            "Product update failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => setLoading(false),
      }
    );
  };

  const handleBack = () => setActiveStep((prev) => prev - 1);

  const handleFinalSubmit = (values) => {
    setLoading(true);
    putMutation.mutate(
      {
        endpoint: `admin/products/${productId}`,
        data: { ...formData, ...values },
      },
      {
        onSuccess: () => {
          toast.success(t("commonToast.dropDownToast.dropDownUpdate"));
          navigate("/products/list");
        },
        onError: (error) => {
          toast.error(
            "DropDown update failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => setLoading(false),
      }
    );
  };

  if (isLoading || loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-red-500">
          Error loading DropDown data. Please try again.
        </div>
      </div>
    );
  }

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <DropDownFormStep1
            dropdown={dropDownData}
            initialData={formData}
            onNext={handleStepOneNext}
            onNextStep={() => setActiveStep((prev) => prev + 1)}
            onCancel={() => navigate("/products/list")}
          />
        );
      case 1:
        return (
          <DropDownFormStep2
            dropdown={dropDownData}
            initialData={formData}
            onNext={handleStepNext}
            onBack={handleBack}
            onNextStep={() => setActiveStep((prev) => prev + 1)}
            onCancel={() => navigate("/products/list")}
          />
        );
      case 2:
        return (
          <DropDownFormStep3
            dropdown={dropDownData}
            initialData={formData}
            onNext={handleStepNext}
            onBack={handleBack}
            onNextStep={() => setActiveStep((prev) => prev + 1)}
            onCancel={() => navigate("/products/list")}
          />
        );
      case 3:
        return (
          <DropDownFormStep4
            dropdown={dropDownData}
            initialData={formData}
            onNext={handleStepNext}
            onBack={handleBack}
            onNextStep={() => setActiveStep((prev) => prev + 1)}
            onCancel={() => navigate("/products/list")}
          />
        );
      default:
        return null;
    }
  };
  return (
    <div className="w-full px-4 py-6">
      <div className="bg-[#F4F8FB] rounded-lg shadow-xl border border-gray-300">
        <div className="px-6 py-4 border-b border-gray-200">
          <Stepper activeStep={activeStep} />
        </div>
        <div className="px-6 py-4">{getStepContent(activeStep)}</div>
      </div>
    </div>
  );
};

export default ProductCircleAddEditPage;
