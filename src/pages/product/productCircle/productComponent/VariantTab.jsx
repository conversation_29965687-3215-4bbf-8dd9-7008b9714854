import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardH<PERSON>er,
  CardTitle,
} from "@/components/ui/product/card";
import { Button } from "@/components/ui/product/button";
import { Input } from "@/components/ui/product/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/product/table";
import { Badge } from "@/components/ui/badge";
import { Trash2, Upload, RefreshCw } from "lucide-react";

export default function ProductVariantsTab({
  attributeList,
  attributeValueMap,
  variants,
  onChange,
  productId = "",
  defaultRegularPrice = 0,
  defaultOfferPrice = 0,
}) {
  const [selectedAttributes, setSelectedAttributes] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Available attributes (excluding already selected ones)
  const getAvailableAttributes = () => {
    const selectedIds = selectedAttributes.map((sa) => sa.attribute.id);
    return attributeList.filter((attr) => !selectedIds.includes(attr.id));
  };

  // Add attribute selection
  const addAttribute = (attributeId) => {
    const attribute = attributeList.find(
      (attr) => attr.id === parseInt(attributeId)
    );
    if (attribute && selectedAttributes.length < 2) {
      setSelectedAttributes([
        ...selectedAttributes,
        {
          attribute,
          selectedValues: [],
        },
      ]);
    }
  };

  // Remove attribute selection
  const removeAttribute = (attributeId) => {
    setSelectedAttributes(
      selectedAttributes.filter((sa) => sa.attribute.id !== attributeId)
    );
  };

  // Update selected values for an attribute
  const updateAttributeValues = (attributeId, valueIds) => {
    const availableValues = attributeValueMap[attributeId] || [];
    const selectedValues = availableValues.filter((value) =>
      valueIds.includes(value.id)
    );

    setSelectedAttributes(
      selectedAttributes.map((sa) =>
        sa.attribute.id === attributeId ? { ...sa, selectedValues } : sa
      )
    );
  };

  // Generate variant combinations
  const generateVariants = () => {
    if (selectedAttributes.length === 0) return;

    setIsGenerating(true);

    // Filter attributes that have selected values
    const validAttributes = selectedAttributes.filter(
      (sa) => sa.selectedValues.length > 0
    );

    if (validAttributes.length === 0) {
      setIsGenerating(false);
      return;
    }

    // Generate all combinations
    const combinations = [];

    if (validAttributes.length === 1) {
      // Single attribute
      const attr = validAttributes[0];
      attr.selectedValues.forEach((value) => {
        combinations.push({
          [attr.attribute.slug]: value.value,
        });
      });
    } else if (validAttributes.length === 2) {
      // Two attributes - create cross product
      const [attr1, attr2] = validAttributes;
      attr1.selectedValues.forEach((value1) => {
        attr2.selectedValues.forEach((value2) => {
          combinations.push({
            [attr1.attribute.slug]: value1.value,
            [attr2.attribute.slug]: value2.value,
          });
        });
      });
    }

    // Convert combinations to variants
    const newVariants = combinations.map((combination, index) => {
      const variantId = `variant_${Date.now()}_${index}`;
      const systemSku = `${productId || "PROD"}-VAR${index + 1}`;

      return {
        id: variantId,
        sku: "",
        system_sku: systemSku,
        barcode: "",
        regular_price: defaultRegularPrice,
        offer_price: defaultOfferPrice,
        vat_tax: "",
        discount_start_date: "",
        discount_end_date: "",
        stock: 0,
        weight: 0,
        length: 0,
        width: 0,
        height: 0,
        is_active: true,
        image: "",
        attributes: combination,
      };
    });

    onChange(newVariants);
    setIsGenerating(false);
  };

  // Update variant field
  const updateVariant = (variantId, field, value) => {
    const updatedVariants = variants.map((variant) =>
      variant.id === variantId ? { ...variant, [field]: value } : variant
    );
    onChange(updatedVariants);
  };

  // Remove variant
  const removeVariant = (variantId) => {
    const updatedVariants = variants.filter(
      (variant) => variant.id !== variantId
    );
    onChange(updatedVariants);
  };

  // Get attribute combination display text
  const getAttributeDisplay = (attributes) => {
    return Object.entries(attributes)
      .map(([key, value]) => value)
      .join(" / ");
  };

  return (
    <div className="space-y-6">
      {/* Attribute Selection Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Product Attributes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add Attribute */}
          {selectedAttributes.length < 2 &&
            getAvailableAttributes().length > 0 && (
              <div className="flex gap-2">
                <Select onValueChange={addAttribute}>
                  <SelectTrigger className="w-64">
                    <SelectValue placeholder="Select an attribute..." />
                  </SelectTrigger>
                  <SelectContent>
                    {getAvailableAttributes().map((attr) => (
                      <SelectItem key={attr.id} value={attr.id.toString()}>
                        {attr.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

          {/* Selected Attributes */}
          <div className="space-y-4">
            {selectedAttributes.map(({ attribute, selectedValues }) => (
              <Card key={attribute.id} className="p-4 bg-gray-50">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{attribute.name}</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeAttribute(attribute.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <Label>Select Values:</Label>
                    <div className="flex flex-wrap gap-2">
                      {(attributeValueMap[attribute.id] || []).map((value) => {
                        const isSelected = selectedValues.some(
                          (sv) => sv.id === value.id
                        );
                        return (
                          <Badge
                            key={value.id}
                            variant={isSelected ? "default" : "outline"}
                            className="cursor-pointer"
                            onClick={() => {
                              const currentIds = selectedValues.map(
                                (sv) => sv.id
                              );
                              const newIds = isSelected
                                ? currentIds.filter((id) => id !== value.id)
                                : [...currentIds, value.id];
                              updateAttributeValues(attribute.id, newIds);
                            }}
                          >
                            {value.value}
                          </Badge>
                        );
                      })}
                    </div>
                    {selectedValues.length > 0 && (
                      <p className="text-sm text-gray-600">
                        Selected:{" "}
                        {selectedValues.map((v) => v.value).join(", ")}
                      </p>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Generate Button */}
          {selectedAttributes.some((sa) => sa.selectedValues.length > 0) && (
            <Button
              onClick={generateVariants}
              disabled={isGenerating}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  Generate Variants (
                  {selectedAttributes.reduce(
                    (total, sa) => total * (sa.selectedValues.length || 1),
                    selectedAttributes.length > 0 ? 1 : 0
                  )}{" "}
                  combinations)
                </>
              )}
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Variants Matrix */}
      {variants.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">
              Variant Matrix ({variants.length} variants)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-32">Combination</TableHead>
                    <TableHead className="min-w-24">SKU</TableHead>
                    <TableHead className="min-w-32">System SKU</TableHead>
                    <TableHead className="min-w-24">Barcode</TableHead>
                    <TableHead className="min-w-24">Regular Price</TableHead>
                    <TableHead className="min-w-24">Offer Price</TableHead>
                    <TableHead className="min-w-24">VAT Tax</TableHead>
                    <TableHead className="min-w-20">Stock</TableHead>
                    <TableHead className="min-w-20">Weight (kg)</TableHead>
                    <TableHead className="min-w-20">Active</TableHead>
                    <TableHead className="min-w-20">Image</TableHead>
                    <TableHead className="min-w-16">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {variants.map((variant) => (
                    <TableRow key={variant.id}>
                      <TableCell>
                        <Badge variant="secondary">
                          {getAttributeDisplay(variant.attributes)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Input
                            value={variant.sku}
                            onChange={(e) =>
                              updateVariant(variant.id, "sku", e.target.value)
                            }
                            placeholder="SKU (min 6 chars)"
                            className={`min-w-24 ${
                              variant.sku && variant.sku.length < 6
                                ? "border-red-500 bg-black"
                                : ""
                            }`}
                            minLength={6}
                          />
                          {variant.sku && variant.sku.length < 6 && (
                            <p className="text-xs text-red-600">
                              SKU must be at least 6 characters
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Input
                          value={variant.system_sku}
                          readOnly
                          className="bg-gray-50 min-w-32"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          value={variant.barcode}
                          onChange={(e) =>
                            updateVariant(variant.id, "barcode", e.target.value)
                          }
                          placeholder="Barcode"
                          className="min-w-24"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          step="0.01"
                          value={variant.regular_price}
                          onChange={(e) =>
                            updateVariant(
                              variant.id,
                              "regular_price",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className="min-w-24"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          step="0.01"
                          value={variant.offer_price}
                          onChange={(e) =>
                            updateVariant(
                              variant.id,
                              "offer_price",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className="min-w-24"
                        />
                      </TableCell>
                      <TableCell>
                        <Select
                          value={variant.vat_tax}
                          onValueChange={(value) =>
                            updateVariant(variant.id, "vat_tax", value)
                          }
                        >
                          <SelectTrigger className="min-w-24">
                            <SelectValue placeholder="VAT" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="exempted">Exempted</SelectItem>
                            <SelectItem value="standard_5">
                              Standard 5%
                            </SelectItem>
                            <SelectItem value="zero_rated">
                              Zero Rated
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={variant.stock}
                          onChange={(e) =>
                            updateVariant(
                              variant.id,
                              "stock",
                              parseInt(e.target.value) || 0
                            )
                          }
                          className="min-w-20"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          step="0.001"
                          value={variant.weight}
                          onChange={(e) =>
                            updateVariant(
                              variant.id,
                              "weight",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          placeholder="0.000"
                          className="min-w-20"
                        />
                      </TableCell>
                      <TableCell>
                        <Switch
                          checked={variant.is_active}
                          onCheckedChange={(checked) =>
                            updateVariant(variant.id, "is_active", checked)
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          className="min-w-20"
                        >
                          <Upload className="w-4 h-4" />
                        </Button>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeVariant(variant.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {variants.length === 0 && selectedAttributes.length === 0 && (
        <Card>
          <CardContent className="py-12 text-center">
            <div className="text-gray-500">
              <h3 className="text-lg font-medium mb-2">No Variants Created</h3>
              <p className="text-sm">
                Select product attributes to generate variants
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
