import React, { useRef, useState } from "react";
import { Upload, ImageIcon } from "lucide-react";
import { useApi } from "@/hooks/useApi";

const ProductMediaUploader = ({ productId, onUploaded }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState("");
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef();

  const { postForm } = useApi();

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };
  const handleDragLeave = () => setIsDragging(false);

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleFiles = async (files) => {
    setError("");
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    const fileList = Array.from(files).filter(
      (file) => allowedTypes.includes(file.type) && file.size <= 5 * 1024 * 1024
    );
    if (!fileList.length) {
      setError("Invalid file type or file too large (max 5MB each).");
      return;
    }
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append("product_id", productId);
      fileList.forEach((file) => {
        formData.append("images[]", file);
      });
      await postForm("admin/product-media/store-update", formData);
      if (onUploaded) onUploaded();
    } catch (err) {
      setError(
        err?.response?.data?.message || "Upload failed. Please try again."
      );
    } finally {
      setUploading(false);
    }
  };

  const handleFileSelect = (e) => {
    handleFiles(e.target.files);
    e.target.value = ""; // allow re-uploading same file
  };

  const handleFileUpload = () => {
    fileInputRef.current.click();
  };

  return (
    <div>
      <div className="text-lg font-medium">Product Media</div>
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragging
            ? "border-blue-400 bg-blue-50"
            : "border-gray-300 hover:border-gray-400"
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          className="hidden"
          onChange={handleFileSelect}
        />

        <ImageIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <div className="space-y-2 flex flex-col items-center">
          <button
            type="button"
            onClick={handleFileUpload}
            disabled={uploading}
            className="flex items-center gap-2 border px-4 py-2 rounded-md bg-white shadow hover:bg-slate-50 text-slate-700"
          >
            <Upload className="w-4 h-4" />
            {uploading ? "Uploading..." : "Upload Images"}
          </button>
          <p className="text-sm text-gray-500">
            Drag and drop images here, or click to browse
          </p>
          <p className="text-xs text-gray-400">
            Supports: JPG, PNG, GIF, WebP (max 5MB each)
          </p>
          {error && <p className="text-xs text-red-500">{error}</p>}
        </div>
      </div>
    </div>
  );
};

export default ProductMediaUploader;
