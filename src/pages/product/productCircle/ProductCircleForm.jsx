import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormInput, FormSelect } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import useProductCategoryList from "@/hooks/list/useProductCategoryList";
import useProductSubCategoryList from "@/hooks/list/useProductSubCategoryList";
import useProductClassList from "@/hooks/list/useProductClassList";
import useProductSubClassList from "@/hooks/list/useProductSubClassList";
import useBrandList from "@/hooks/list/useBrandList";
import { FaHome } from "react-icons/fa";

const ProductCircleForm = ({ dropdown, onSubmit, onCancel }) => {
  const { t } = useTranslation();
  const { options: categoryOptions, loading: categoryLoading } =
    useProductCategoryList();

  const { options: brandOptions, loading: brandLoading } = useBrandList();

  const initialValues = dropdown
    ? {
        id: dropdown.id,
        category_id: dropdown.category_id || "",
        sub_category_id: dropdown.sub_category_id || "",
        class_id: dropdown.class_id || "",
        sub_class_id: dropdown.sub_class_id || "",
        brand_id: dropdown.brand_id || "",
        vendor_sku: dropdown.vendor_sku || "",
        barcode: dropdown.barcode || "",
        model_number: dropdown.model_number || "",
      }
    : {
        category_id: "",
        sub_category_id: "",
        class_id: "",
        sub_class_id: "",
        brand_id: "",
        vendor_sku: "",
        barcode: "",
        model_number: "",
      };

  const validationSchema = Yup.object({
    category_id: Yup.string().required(t("commonValidation.category_required")),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    onSubmit(values);
    setSubmitting(false);
  };

  // const handleCategoryClick = (categoryId) => {
  //   setFieldValue("category_id", categoryId);
  // };
  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => {
        const { options: subCategoryOptions, loading: subLoading } =
          useProductSubCategoryList(values.category_id);

        const { options: classOptions, loading: classLoading } =
          useProductClassList(values.category_id, values.sub_category_id);

        const { options: subClassOptions, loading: subClassLoading } =
          useProductSubClassList(values.class_id);

        const handleCategoryClick = (categoryId) => {
          setFieldValue(
            "category_id",
            values.category_id === categoryId ? "" : categoryId
          );
        };

        return (
          <Form className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow border">
              <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                {/* <FormSelect
                  name="category_id"
                  label="Category"
                  options={categoryOptions}
                  required
                  disabled={categoryLoading}
                /> */}
                {!categoryLoading ? (
                  categoryOptions.map((category) => (
                    <Button
                      key={category.value}
                      type="button"
                      variant="outline"
                      onClick={() => handleCategoryClick(category.value)}
                      className={`${
                        values.category_id === category.value
                          ? "bg-primary-500 text-slate-50 border-primary-400 shadow-lg border-4"
                          : "bg-slate-200 text-slate-900 border-slate-400"
                      }bg-slate-200 text-slate-900 border-slate-400 hover:text-slate-50 hover:border-primary-400 hover:shadow-xl hover:bg-primary-500`}
                    >
                      <div className="space-y-1 flex flex-col items-center justify-center py-5 text-base">
                        <FaHome size={28} />
                        {category.label}
                      </div>
                    </Button>
                  ))
                ) : (
                  <Button type="button" variant="outline" disabled>
                    Loading...
                  </Button>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <FormSelect
                name="sub_category_id"
                label="Sub Category"
                options={subCategoryOptions}
                required
                disabled={!values.category_id || subLoading}
              />

              <FormSelect
                name="class_id"
                label="Class"
                options={classOptions}
                required
                disabled={classLoading || !values.sub_category_id}
              />
              <FormSelect
                name="sub_class_id"
                label="Sub class"
                options={subClassOptions}
                required
                disabled={!values.class_id || subClassLoading}
              />

              <FormSelect
                name="brand_id"
                label="Brand"
                options={brandOptions}
                required
                disabled={brandLoading}
              />
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <h4 className="font-semibold text-yellow-800 mb-3">
                Product Code Configuration
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormInput
                  name="vendor_sku"
                  label="Product Code"
                  placeholder={t("commonPlaceholder.name_enPlaceholder")}
                  required
                />
                <FormInput
                  name="barcode"
                  label="Bar Code"
                  placeholder={t("commonPlaceholder.name_arPlaceholder")}
                />
                <FormInput
                  name="model_number"
                  label="Model Number"
                  placeholder={t("commonPlaceholder.name_arPlaceholder")}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end pt-6 border-t border-gray-200 mt-8 gap-3">
              <Button type="button" variant="outline" onClick={onCancel}>
                {t("commonButton.cancel")}
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {dropdown
                  ? t("commonButton.dropDown.updated")
                  : t("commonButton.dropDown.create")}
              </Button>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default ProductCircleForm;
