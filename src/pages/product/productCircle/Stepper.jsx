import { useNavigate, useParams } from "react-router-dom";

const steps = [
  { label: "Classification" },
  { label: "Product Details" },
  { label: "Product Media" },
  { label: "Pricing & Inventory" },
  { label: "Compliance & Fulfillment" },
  { label: "SEO & FAQs" },
  { label: "Review & Submit" },
];

const Stepper = ({ activeStep }) => (
  <div className="flex items-center w-full overflow-x-auto py-2">
    {steps.map((step, idx) => (
      <div key={step.label} className="flex items-center">
        <div
          className={`flex flex-col items-center ${
            idx === activeStep ? "text-blue-600" : "text-gray-400"
          }`}
        >
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center font-bold
            ${idx === activeStep ? "bg-blue-600 text-white" : "bg-gray-200"}
          `}
          >
            {idx + 1}
          </div>
          <span
            className={`mt-1 text-sm font-medium ${
              idx === activeStep ? "text-blue-600" : "text-gray-400"
            }`}
          >
            {step.label}
          </span>
        </div>
        {idx !== steps.length - 1 && (
          <div className="mx-2 w-8 h-1 bg-gray-200 rounded" />
        )}
      </div>
    ))}
  </div>
);

export default Stepper;
