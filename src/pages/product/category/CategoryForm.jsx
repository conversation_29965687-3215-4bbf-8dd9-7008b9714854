import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormInput, FormRadioGroup, FormTextarea } from "@/components/ui/form";
import ImageUpload from "@/components/ui/ImageUpload";
import ImageUploader from "@/components/ui/ImageUploader";
import Button from "@/components/ui/Button";
import useProductCategoryList from "@/hooks/list/useProductCategoryList";
import { useTranslation } from "react-i18next";
import { FaCheckCircle, FaTimesCircle } from "react-icons/fa";

const CategoryForm = ({ category, onSubmit, onCancel }) => {
  const { t } = useTranslation();

  const initialValues = category
    ? {
        id: category.id,
        code: category.code || "",
        fee_text: category.fee_text || "",
        name: category.name || "",
        name_ar: category.name_ar || "",
        ordering_number: category.ordering_number || "0",
        banner: category.banner_url || "",
        cover_image: category.cover_image_url || "",
        icon: category.icon_url || "",
        slug: category.slug || "",
        status: category.status || "inactive",
        type: category.type || "sub",
        meta_description: category.meta_description || "",
        meta_title: category.meta_title || "",
        parent_id: category?.parent_id || "",
      }
    : {
        code: "",
        fee_text: "",
        name: "",
        name_ar: "",
        slug: "",
        status: "inactive",
        type: "sub",
        ordering_number: "0",
        banner: "",
        cover_image: "",
        icon: "",
        meta_description: "",
        meta_title: "",
        parent_id: "",
      };

  const validationSchema = Yup.object({
    name: Yup.string().required(t("commonValidation.name_en")),
    type: Yup.string().required(t("commonValidation.category_type")),
    slug: Yup.string().required(t("commonValidation.slug")),
    status: Yup.string().required(t("commonValidation.status")),
  });

  const categoryTypeOptions = [
    { label: t("commonOptions.categoryType.main"), value: "main" },
    { label: t("commonOptions.categoryType.sub"), value: "sub" },
  ];

  const statusOptions = [
    { label: t("commonOptions.status.active"), value: "active" },
    { label: t("commonOptions.status.inactive"), value: "inactive" },
  ];

  const { options: categoryOptions } = useProductCategoryList();

  const handleSubmit = (values, { setSubmitting }) => {
    const formattedValues = {
      ...values,
      banner: values.banner?.path || values.banner || "",
      icon: values.icon?.path || values.icon || "",
      cover_image: values.cover_image?.path || values.cover_image || "",
    };
    onSubmit(formattedValues);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => (
        <Form className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="gap-4 bg-white p-5 space-y-5">
                <div className="grid grid-cols-2 gap-4">
                  <FormInput
                    name="code"
                    label={t("commonField.code")}
                    placeholder={t("commonPlaceholder.codePlaceholder")}
                  />
                  <FormInput
                    name="fee_text"
                    label={t("commonField.fee_text")}
                    placeholder={t("commonPlaceholder.fee_textPlaceholder")}
                  />
                </div>
                <FormInput
                  name="name"
                  label={t("commonField.name_en")}
                  placeholder={t("commonPlaceholder.name_enPlaceholder")}
                  required
                />
                <FormInput
                  name="name_ar"
                  label={t("commonField.name_ar")}
                  placeholder={t("commonPlaceholder.name_arPlaceholder")}
                />
                <FormInput
                  name="slug"
                  label={t("commonField.slug")}
                  placeholder={t("commonPlaceholder.slugPlaceholder")}
                />
              </div>
              <div className="gap-4 bg-white p-5 space-y-5">
                <span className="my-4 text-lg font-semibold">
                  {t("commonField.meta_options")}
                </span>
                <FormInput
                  name="meta_title"
                  label={t("commonField.meta_title_en")}
                  placeholder={t("commonPlaceholder.meta_titlePlaceholder")}
                />
                <FormTextarea
                  name="meta_description"
                  label={t("commonField.meta_description")}
                  placeholder={t(
                    "commonPlaceholder.meta_descriptionPlaceholder"
                  )}
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className=" bg-white p-5">
                <FormRadioGroup
                  name="type"
                  label={t("commonField.category_type")}
                  options={categoryTypeOptions}
                  required
                  onChange={(val) => {
                    if (!val) setFieldValue("parent_id", null);
                  }}
                />
                {values.type === "sub" && (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      {t("commonField.selectMainCategory")}
                    </label>
                    <div className="grid grid-cols-3 gap-4">
                      {categoryOptions
                        .filter((opt) => opt.value !== "")
                        .map((option) => {
                          const isSelected =
                            values.parent_id?.toString() ===
                            option.value.toString();
                          return (
                            <button
                              key={option.value}
                              type="button"
                              onClick={() =>
                                setFieldValue("parent_id", parseInt(option.value, 10))
                              }
                              className={`p-3 border rounded text-sm flex items-center justify-between transition-all ${
                                isSelected
                                  ? "bg-sky-100 text-sky-800 border-sky-500"
                                  : "bg-slate-100 text-gray-700 hover:bg-sky-50"
                              }`}
                            >
                              <span>{option.label}</span>
                              {isSelected ? (
                                <FaCheckCircle className="text-green-500 ml-2" />
                              ) : (
                                // <FaTimesCircle className="text-red-400 ml-2" />
                                ""
                              )}
                            </button>
                          );
                        })}
                    </div>
                  </div>
                )}
                {/* {values.type === "sub" && (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      {t("commonField.selectMainCategory")}
                    </label>
                    <div className="grid grid-cols-3 gap-4">
                      {categoryOptions
                        .filter((opt) => opt.value !== "")
                        .map((option) => (
                          <button
                            key={option.value}
                            type="button"
                            onClick={() =>
                              setFieldValue("parent_id", option.value)
                            }
                            className={`p-3 border rounded text-sm transition-all ${
                              values.parent_id?.toString() ===
                              option.value.toString()
                                ? "bg-sky-500 text-white border-sky-600"
                                : "bg-slate-200 text-gray-700 hover:bg-sky-50"
                            }`}
                          >
                            <FaEdit className="text-indigo-600" />
                            {option.label}
                          </button>
                        ))}
                    </div>
                  </div>
                )} */}
                <div className="my-4">
                  <FormRadioGroup
                    name="status"
                    label={t("commonField.status")}
                    options={statusOptions}
                    required
                  />
                </div>

                <div className="mb-4">
                  <FormInput
                    name="ordering_number"
                    label={t("commonField.ordering_serial")}
                    placeholder={t(
                      "commonPlaceholder.ordering_serialPlaceholder"
                    )}
                  />
                </div>

                <ImageUpload
                  value={initialValues.banner}
                  onUploadSuccess={(url) => setFieldValue("banner", url)}
                />
                {/* <ImageUploader
                  title={t("commonField.banner")}
                  value={category?.banner?.path || category?.banner || ""}
                  onChange={(file) => setFieldValue("banner", file)}
                /> */}

                <div className="grid grid-cols-2 gap-4 my-4">
                  {/* <ImageUpload
                    value={initialValues.cover_image}
                    onUploadSuccess={(url) => setFieldValue("cover_image", url)}
                  /> */}
                  <ImageUpload
                    value={initialValues.cover_image}
                    onUploadSuccess={(url) => setFieldValue("cover_image", url)}
                  />
                  <ImageUpload
                    value={initialValues.icon}
                    onUploadSuccess={(url) => setFieldValue("icon", url)}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end pt-4 border-t border-gray-200 mt-8 gap-3">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("commonButton.cancel")}
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {category
                ? t("commonButton.category.updated")
                : t("commonButton.category.create")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default CategoryForm;
