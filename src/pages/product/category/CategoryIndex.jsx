import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import CategoryFilter from "./CategoryFilters";
import { FaEdit, FaTrash, <PERSON>a<PERSON><PERSON><PERSON>List, FaPlus } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";

const CategoryIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [previewCategory, setPreviewCategory] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: "active",
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: categoryData,
    isLoading,
    isError: categoryError,
    refetch,
  } = fetchData("admin/categories", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    ...filterOptions,
  });

  const categoryList = categoryData?.data?.data || [];
  const paginationInfo = {
    currentPage: categoryData?.data?.current_page || 1,
    perPage: categoryData?.data?.per_page || itemsPerPage,
    totalItems: categoryData?.data?.total_items || 0,
    totalPages: categoryData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditCategory = (category) => {
    navigate(`/products/categories/edit/${category.id}`);
  };

  const handleDeleteClick = (category) => {
    setSelectedCategory(category);
  };

  const handleDeleteCategory = async () => {
    if (!selectedCategory) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/categories/${selectedCategory.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedCategory(null);
          refetch();
          // toast.success("Category deleted successfully.");
          toast.success(t("commonToast.categoryToast.categoryDelete"));
        },
        onError: (error) => {
          console.error(
            "Category deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Category deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditCategory = (category) => true;

  const canDeleteCategory = (category) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("category.columns.id"),
      accessor: "id",
    },
    {
      header: t("category.columns.name_en"),
      accessor: "name",
    },
    {
      header: t("category.columns.name_ar"),
      accessor: "name_ar",
    },
    {
      header: t("category.columns.code"),
      accessor: "code",
    },
    {
      header: t("category.columns.type"),
      accessor: "type",
    },

    {
      header: t("category.columns.slug"),
      accessor: "slug",
    },
    {
      header: t("category.columns.status"),
      accessor: "status",
      render: (row) => {
        const statusColors = {
          active: "bg-green-100 text-green-800",
          inactive: "bg-red-100 text-red-600",
        };

        return (
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
              statusColors[row.status]
            }`}
          >
            {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
          </span>
        );
      },
    },
    {
      header: t("category.columns.actions"),
      render: (row) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditCategory(row)}
            disabled={!canEditCategory(row)}
            title={
              !canEditCategory(row)
                ? "You don't have permission to edit this Category"
                : t("category.editAction")
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteCategory(row)}
            title={
              !canDeleteCategory(row)
                ? "You can't delete this Category"
                : t("category.deleteAction")
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="max-w-7xl mx-auto relative pt-6">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("category.title")}
          icon={<FaClipboardList className="text-indigo-600 me-1" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/products/categories/add")}
            >
              <FaPlus className="mr-2" /> {t("category.add")}
            </Button>
          }
        >
          <CategoryFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={categoryList}
            emptyMessage={t("category.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      {selectedCategory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">
              {t("commonDelete.deleteTitle")}
            </h3>
            <p className="text-gray-700 mb-6">
              {t("commonDelete.deleteMessage")}{" "}
              <span className="font-bold mx-1">Category</span>
              <span className="font-semibold">{selectedCategory?.name}</span>?
              {t("commonDelete.deleteWarning")}
            </p>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setSelectedCategory(null)}
              >
                {t("commonButton.cancel")}
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteCategory}
                disabled={deleteLoading}
              >
                {deleteLoading ? (
                  <LoadingSpinner size={20} />
                ) : (
                  t("commonButton.delete")
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategoryIndex;
