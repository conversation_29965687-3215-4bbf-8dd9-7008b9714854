import { Formik, Form, FieldArray } from "formik";
import * as Yup from "yup";
import { FormInput } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import { FaTrash, FaRegPlusSquare } from "react-icons/fa";

const attributeForm = ({ attribute, onSubmit, onCancel }) => {
  const { t } = useTranslation();

  const initialValues = attribute
    ? {
        id: attribute.id,
        name: attribute.name || "",
        name_ar: attribute.name_ar || "",
        status: attribute.status || "inactive",
        values: attribute.values || [{ value: "", value_ar: "" }],
      }
    : {
        name: "",
        name_ar: "",
        status: "inactive",
        values: [{ value: "", value_ar: "" }],
      };

  const validationSchema = Yup.object({
    name: Yup.string().required(t("commonValidation.name_en")),
    status: Yup.string().required(t("commonValidation.status")),
    values: Yup.array()
      .min(1, t("commonValidation.atLeastOneValue"))
      .of(
        Yup.object().shape({
          value: Yup.string().required(t("commonValidation.value_en")),
          value_ar: Yup.string(),
        })
      )
      .required(t("commonValidation.valuesRequired")),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    const formattedValues = {
      name: values.name,
      name_ar: values.name_ar,
      status: values.status,
      values: values.values,
    };

    values.values.forEach((v, index) => {
      formattedValues[`values[merge_input][${index}][value]`] = v.value;
      if (v.value_ar) {
        formattedValues[`values[merge_input][${index}][value_ar]`] = v.value_ar;
      }
    });

    onSubmit(formattedValues);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, values }) => (
        <Form className="space-y-6">
          {/* Basic Information */}
          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                name="name"
                label={t("commonField.name_en")}
                placeholder={t("commonPlaceholder.name_enPlaceholder")}
                required
              />
              <FormInput
                name="name_ar"
                label={t("commonField.name_ar")}
                placeholder={t("commonPlaceholder.name_arPlaceholder")}
              />
            </div>
          </div>

          {/* Values List */}
          <div className="bg-white p-6 rounded-lg shadow border">
            <FieldArray name="values">
              {({ push, remove }) => (
                <div className="space-y-6">
                  <div className="flex justify-between items-center border-b pb-2">
                    <h3 className="text-lg font-semibold text-gray-800">
                      {t("attributes.valuesTitle")}
                    </h3>
                    <Button
                      type="button"
                      variant="primary"
                      onClick={() => push({ value: "", value_ar: "" })}
                    >
                      <FaRegPlusSquare className="mx-2" />
                      {t("attributes.addValue")}
                    </Button>
                  </div>

                  {values.values.map((_, index) => (
                    <div
                      key={index}
                      className="grid grid-cols-1 md:grid-cols-12 gap-4 items-end bg-gray-50 p-4 rounded-md border"
                    >
                      <div className="md:col-span-5 w-full">
                        <FormInput
                          name={`values[${index}].value`}
                          label={t("commonField.value_en")}
                          placeholder={t(
                            "commonPlaceholder.value_enPlaceholder"
                          )}
                          required
                        />
                      </div>
                      <div className="md:col-span-5 w-full">
                        <FormInput
                          name={`values[${index}].value_ar`}
                          label={t("commonField.value_ar")}
                          placeholder={t(
                            "commonPlaceholder.value_arPlaceholder"
                          )}
                        />
                      </div>
                      <div className="md:col-span-2 flex justify-end">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => remove(index)}
                          className="text-red-600"
                          // disabled={index === 0}
                          disabled={values.values.length === 1}
                        >
                          <FaTrash className="mx-2" />
                          {t("commonButton.remove")}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </FieldArray>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end pt-6 border-t border-gray-200 mt-8 gap-3">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("commonButton.cancel")}
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {attribute
                ? t("commonButton.attribute.updated")
                : t("commonButton.attribute.create")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default attributeForm;
