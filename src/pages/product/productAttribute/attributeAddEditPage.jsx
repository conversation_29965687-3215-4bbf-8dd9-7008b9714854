import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { toast } from "sonner";
import AttributeFormPage from "./attributeForm";
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { useTranslation } from "react-i18next";

const attributeAddEditPage = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { fetchData, postMutation, putMutation } = useApi();

  const { data, isLoading, isError, refetch } = id
    ? fetchData(`admin/product-attributes/${id}`)
    : { data: null, isLoading: false, isError: false, refetch: () => {} };
  const attributeData = data?.data;

  const handleSubmit = (values) => {
    setLoading(true);
    const dataToSend = { ...values };

    const onSuccess = () => {
      toast.success(
        t(
          id
            ? "commonToast.attributeToast.attributeUpdate"
            : "commonToast.attributeToast.attributeCreate"
        )
      );

      navigate("/products/attribute");
    };

    const onError = (error) => {
      console.error(
        "Attribute update failed:",
        error?.response?.data || error.message
      );
      toast.error(
        "Attribute update failed: " +
          (error?.response?.data?.message || error.message)
      );
    };

    const onSettled = () => setLoading(false);

    if (!id) {
      postMutation.mutate(
        { endpoint: "admin/product-attributes", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/product-attributes/${id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  if (isLoading || loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-red-500">
          Error loading Attribute data. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="w-full px-4 py-6">
      <div className="bg-[#F4F8FB] rounded-lg shadow-xl border border-gray-300">
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate("/products/attribute")}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
            <h3 className="text-lg font-medium text-gray-900">
              {t(id ? "attributes.editAction" : "attributes.add")}
            </h3>
          </div>
        </div>
        <div className="px-6 py-4">
          <AttributeFormPage
            attribute={attributeData}
            onSubmit={handleSubmit}
            onCancel={() => navigate("/products/attribute")}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default attributeAddEditPage;
