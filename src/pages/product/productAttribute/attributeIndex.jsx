import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import AttributeFilter from "./attributeFilters";
import {
  FaEdit,
  FaTrash,
  FaClipboardList,
  FaPlus,
  FaBorderStyle,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";

const attributeIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedAttribute, setSelectedAttribute] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: true,
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: attributeData,
    isLoading,
    isError: attributeError,
    refetch,
  } = fetchData("admin/product-attributes", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    ...filterOptions,
  });

  const attributeList = attributeData?.data?.data || [];
  const paginationInfo = {
    currentPage: attributeData?.data?.current_page || 1,
    perPage: attributeData?.data?.per_page || itemsPerPage,
    totalItems: attributeData?.data?.total_items || 0,
    totalPages: attributeData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditAttribute = (attribute) => {
    navigate(`/products/attribute/edit/${attribute.id}`);
  };

  const handleDeleteClick = (attribute) => {
    setSelectedAttribute(attribute);
  };

  const handleDeleteAttribute = async () => {
    if (!selectedAttribute) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/product-attributes/${selectedAttribute.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedAttribute(null);
          refetch();
          toast.success(t("commonToast.attributeToast.attributeDelete"));
        },
        onError: (error) => {
          console.error(
            "= deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Attribute deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditAttribute = (attribute) => true;

  const canDeleteAttribute = (attribute) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name",
    },
    {
      header: t("commonTableLabel.name_ar"),
      accessor: "name_ar",
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditAttribute(row)}
            disabled={!canEditAttribute(row)}
            title={
              !canEditAttribute(row)
                ? "You don't have permission to edit this Attribute"
                : t("attributes.editAction")
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteAttribute(row)}
            title={
              !canDeleteAttribute(row)
                ? "You can't delete this Attribute"
                : t("attributes.deleteAction")
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="max-w-7xl mx-auto relative pt-6">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("attributes.title")}
          icon={<FaClipboardList className="text-indigo-600 me-1" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/products/attribute/add")}
            >
              <FaPlus className="mr-2" /> {t("attributes.add")}
            </Button>
          }
        >
          <AttributeFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={attributeList}
            emptyMessage={t("attributes.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      {selectedAttribute && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">
              {t("commonDelete.deleteTitle")}
            </h3>
            <p className="text-gray-700 mb-6">
              {t("commonDelete.deleteMessage")}{" "}
              <span className="font-bold mx-1">Attribute</span>
              <span className="font-semibold">{selectedAttribute?.name}</span>?
              {t("commonDelete.deleteWarning")}
            </p>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setSelectedAttribute(null)}
              >
                {t("commonButton.cancel")}
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteAttribute}
                disabled={deleteLoading}
              >
                {deleteLoading ? (
                  <LoadingSpinner size={20} />
                ) : (
                  t("commonButton.delete")
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default attributeIndex;
