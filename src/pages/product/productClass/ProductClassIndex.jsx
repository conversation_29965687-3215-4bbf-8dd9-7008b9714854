import { useState } from "react";
import { motion } from "framer-motion";
import {
  FaUserPlus,
  FaEdit,
  FaTrash,
  FaUserShield,
  FaKey,
  FaStar,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import ProductClassFilters from "./ProductClassFilters";
import ProductClassEditForm from "./ProductClassAddUpdate";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";

const ProductClassIndex = () => {
  const { deleteMutation, postMutation, putMutation } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // State for filters
  const [filterOptions, setFilterOptions] = useState({
    search: "",
    roles: [],
    statuses: [],
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);

  const {
    data: classesData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/product-classes", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    ...filterOptions
  });

  const classList = classesData?.data?.data || [];
  const paginationInfo = {
    currentPage: classesData?.data?.current_page || 1,
    perPage: classesData?.data?.per_page || itemsPerPage,
    totalItems: classesData?.data?.total_items || 0,
    totalPages: classesData?.data?.total_pages || 1,
  };

  // Handle filter change from ProductClassFilters
  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1)
  };

  // Open edit modal
  const handleEditClass = (classItem) => {
    setSelectedClass(classItem);
    setEditModalOpen(true);
  };

  // Open delete modal
  const handleDeleteClick = (classItem) => {
    setSelectedClass(classItem);
    setDeleteModalOpen(true);
  };

  const handleDeleteClass = async () => {
    if (!selectedClass) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/product-classes/${selectedClass.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedClass(null);
          refetch();
          toast.success("Class Deleted Successfully.");
        },
        onError: (error) => {
          console.error(
            "Class deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdateClass = (updatedClass) => {
    setEditLoading(true);
    const dataToSend = { ...updatedClass };
    delete dataToSend.confirmPassword;

    if (updatedClass.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedClass(null);
      refetch();
      toast.success(
        `Class ${updatedClass.id ? "updated" : "created"} successfully.`
      );
    };
    const onError = (error) => {
      console.error(
        "Class update failed:",
        error?.response?.data || error.message
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedClass.id) {
      postMutation.mutate(
        { endpoint: "admin/product-classes", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/product-classes/${updatedClass.id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  const canEditClass = (classItem) => true

  const canDeleteClass = (classItem) => true

  // Table columns configuration
  const columns = [
    {
      header: "ID",
      accessor: "id",
    },
    {
      header: "Name",
      accessor: "name",
    },
    {
      header: "Category",
      accessor: "category_id",
    },
    {
      header: "code",
      accessor: "code",
    },
    {
      header: "Popular",
      accessor: "is_popular",
      render: (row) => (row.is_popular ? "Yes" : "No"),
    },
    {
      header: "Status",
      accessor: "status",
      render: (row) => {
        const statusColors = {
          active: "bg-green-100 text-green-800",
          inactive: "bg-red-100 text-red-600",
        };

        return (
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
              statusColors[row.status]
            }`}
          >
            {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
          </span>
        );
      },
    },

    {
      header: "Actions",
      render: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditClass(row)}
            disabled={!canEditClass(row)}
            title={
              !canEditClass(row)
                ? "You don't have permission to edit this class"
                : "Edit class"
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteClass(row)}
            title={
              !canDeleteClass(row) ? "You can't delete this class" : "Delete class"
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="max-w-7xl mx-auto relative">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title="Class List"
          icon={<FaUserShield className="text-indigo-600" />}
          action={
            <Button
              variant="primary"
              onClick={() => {
                setSelectedClass(null);
                setEditModalOpen(true);
              }}
            >
              <FaUserPlus className="mr-2" /> Add Class
            </Button>
          }
        >
          <ProductClassFilters
            onChange={handleFilterChange}
          />

          <Table
            columns={columns}
            data={classList}
            emptyMessage="No class found matching your criteria."
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>

      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={selectedClass ? "Edit Class" : "Add New Class"}
        size="lg"
      >
        <ProductClassEditForm
          class={selectedClass}
          onSubmit={handleUpdateClass}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Confirm Delete"
        size="sm"
      >
        <div className="py-4">
          <p className="text-gray-700">
            Are you sure you want to delete the class{" "}
            <span className="font-semibold">{selectedClass?.name}</span>? This
            action cannot be undone.
          </p>
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteClass}
            disabled={deleteLoading}
          >
            {deleteLoading ? <LoadingSpinner size={20} /> : "Delete"}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default ProductClassIndex;
