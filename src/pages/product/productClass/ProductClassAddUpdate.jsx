import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormInput, FormRadioGroup, FormSelect } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import useProductCategoryList from "@/hooks/list/useProductCategoryList";
import useProductSubCategoryList from "@/hooks/list/useProductSubCategoryList";
import useMainClassList from "@/hooks/list/useMainClassList";

const ProductClassAddUpdate = ({ class: classItem, onSubmit, onCancel }) => {
  const { options: categoryOptions, loading: categoryLoading } =
    useProductCategoryList();

  const initialValues = classItem
    ? {
        id: classItem.id,
        name: classItem.name || "",
        code: classItem.code || "",
        category_id: classItem.category_id || "",
        sub_category_id: classItem.sub_category_id || "",
        is_popular: classItem.is_popular === true,
        has_parent: classItem.parent_id ? true : false,
        parent_id: classItem.parent_id || null,
        status: classItem.status || "active",
      }
    : {
        name: "",
        code: "",
        category_id: "",
        sub_category_id: "",
        parent_id: null,
        has_parent: false,
        is_popular: false,
        status: "active",
      };

  const validationSchema = Yup.object({
    name: Yup.string().required("Name is required"),
    category_id: Yup.string().required("Category is required"),
    sub_category_id: Yup.string().required("Subcategory is required"),
    status: Yup.string().required("Status is required"),
  });

  const isPopularOptions = [
    { label: "Yes", value: true },
    { label: "No", value: false },
  ];

  const statusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
  ];

  const handleSubmit = (values, { setSubmitting }) => {
    const { has_parent, ...restValues } = values;
    const formattedValues = {
      ...restValues,
      is_popular: Boolean(values.is_popular),
    };
    onSubmit(formattedValues);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => {
        const { options: subCategoryOptions, loading: subLoading } =
          useProductSubCategoryList(values.category_id);

        const { options: parentCategoryOptions, loading: parentLoading } =
          useMainClassList(values.category_id);

        return (
          <Form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormSelect
                name="category_id"
                label="Category"
                options={categoryOptions}
                required
                disabled={categoryLoading}
              />

              <FormSelect
                name="sub_category_id"
                label="Subcategory"
                options={subCategoryOptions}
                required
                disabled={!values.category_id || subLoading}
              />

              <FormInput name="name" label="Name" required />
              <FormInput name="code" label="Code" />
              
              <FormRadioGroup
                name="has_parent"
                label="Has Main Class?"
                options={isPopularOptions}
                onChange={(val) => {
                  if (!val) setFieldValue("parent_id", null);
                }}
              />
              {values.has_parent && (
                <FormSelect
                  name="parent_id"
                  label="Main Class"
                  options={parentCategoryOptions}
                  disabled={!values.category_id || parentLoading}
                />
              )}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormRadioGroup
                name="is_popular"
                label="Is Popular"
                options={isPopularOptions}
              />
              <FormRadioGroup
                name="status"
                label="Status"
                options={statusOptions}
                required
              />
            </div>

            <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-6">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" variant="primary" loading={isSubmitting}>
                {classItem ? "Update Class" : "Create Class"}
              </Button>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default ProductClassAddUpdate;
