import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import Card from '../../components/ui/Card';
import Table from '../../components/ui/Table';
import Pagination from '../../components/ui/Pagination';
import SearchInput from '../../components/ui/SearchInput';
import FilterDropdown from '../../components/ui/FilterDropdown';
import Button from '../../components/ui/Button';
import { generateMockProducts, filterProducts, paginateProducts } from '../../utils/mockData';

const ListDemo = () => {
  const { t } = useTranslation();

  // State for all products
  const [allProducts, setAllProducts] = useState([]);

  // State for filtered products
  const [filteredProducts, setFilteredProducts] = useState([]);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    categories: [],
    statuses: [],
  });

  // State for displayed products (after pagination)
  const [displayedProducts, setDisplayedProducts] = useState([]);

  // Load mock data on component mount
  useEffect(() => {
    const products = generateMockProducts(50);
    setAllProducts(products);
    setFilteredProducts(products);
  }, []);

  // Apply filters and search when they change
  useEffect(() => {
    const filtered = filterProducts(allProducts, searchTerm, filters);
    setFilteredProducts(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [allProducts, searchTerm, filters]);

  // Apply pagination when filtered products or current page changes
  useEffect(() => {
    const paginated = paginateProducts(filteredProducts, currentPage, itemsPerPage);
    setDisplayedProducts(paginated);
  }, [filteredProducts, currentPage, itemsPerPage]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search change
  const handleSearchChange = (value) => {
    setSearchTerm(value);
  };

  // Handle filter change
  const handleFilterChange = (filterType, values) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: values,
    }));
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1); // Reset to first page when items per page changes
  };

  // Reset all filters
  const handleResetFilters = () => {
    setSearchTerm('');
    setFilters({
      categories: [],
      statuses: [],
    });
  };

  // Table columns configuration
  const columns = [
    {
      header: t('demo.list.columns.id'),
      accessor: 'id',
    },
    {
      header: t('demo.list.columns.name'),
      accessor: 'name',
    },
    {
      header: t('demo.list.columns.category'),
      accessor: 'category',
    },
    {
      header: t('demo.list.columns.price'),
      accessor: 'price',
      render: (row) => `AED ${row.price.toFixed(2)}`,
    },
    {
      header: t('demo.list.columns.rating'),
      accessor: 'rating',
      render: (row) => {
        const fullStars = Math.floor(row.rating);
        const hasHalfStar = row.rating % 1 >= 0.5;

        return (
          <div className="flex items-center rtl-flex-row-reverse">
            {Array.from({ length: 5 }).map((_, index) => (
              <svg
                key={index}
                className={`h-4 w-4 ${
                  index < fullStars
                    ? 'text-yellow-400'
                    : index === fullStars && hasHalfStar
                    ? 'text-yellow-400'
                    : 'text-gray-300'
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
            <span className="mr-1 rtl-mr-0 rtl-ml-1 text-gray-600">{row.rating}</span>
          </div>
        );
      },
    },
    {
      header: t('demo.list.columns.status'),
      accessor: 'status',
      render: (row) => {
        const statusMap = {
          'In Stock': t('demo.list.statuses.inStock'),
          'Low Stock': t('demo.list.statuses.lowStock'),
          'Out of Stock': t('demo.list.statuses.outOfStock'),
          'Discontinued': t('demo.list.statuses.discontinued'),
        };

        const statusColors = {
          'In Stock': 'bg-green-100 text-green-800',
          'Low Stock': 'bg-yellow-100 text-yellow-800',
          'Out of Stock': 'bg-red-100 text-red-800',
          'Discontinued': 'bg-gray-100 text-gray-800',
        };

        return (
          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[row.status]}`}>
            {statusMap[row.status]}
          </span>
        );
      },
    },
  ];

  // Filter options
  const categoryOptions = [
    { label: t('demo.list.categories.electronics'), value: 'Electronics' },
    { label: t('demo.list.categories.clothing'), value: 'Clothing' },
    { label: t('demo.list.categories.homeKitchen'), value: 'Home & Kitchen' },
    { label: t('demo.list.categories.books'), value: 'Books' },
    { label: t('demo.list.categories.toys'), value: 'Toys' },
  ];

  const statusOptions = [
    { label: t('demo.list.statuses.inStock'), value: 'In Stock' },
    { label: t('demo.list.statuses.lowStock'), value: 'Low Stock' },
    { label: t('demo.list.statuses.outOfStock'), value: 'Out of Stock' },
    { label: t('demo.list.statuses.discontinued'), value: 'Discontinued' },
  ];

  // Calculate total pages
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  return (
    <div className="max-w-7xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card title={t('demo.list.title')}>
          <div className="mb-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4 rtl-md-space-x-reverse">
              <div className="flex-1 max-w-md">
                <SearchInput
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder={t('demo.list.search')}
                />
              </div>

              <div className="flex flex-wrap items-center space-x-2 rtl-space-x-reverse">
                <FilterDropdown
                  label={t('demo.list.category')}
                  options={categoryOptions}
                  selectedValues={filters.categories}
                  onChange={(values) => handleFilterChange('categories', values)}
                />

                <FilterDropdown
                  label={t('demo.list.status')}
                  options={statusOptions}
                  selectedValues={filters.statuses}
                  onChange={(values) => handleFilterChange('statuses', values)}
                />

                <Button
                  variant="outline"
                  onClick={handleResetFilters}
                  className="ml-2 rtl-ml-0 rtl-mr-2"
                >
                  {t('demo.list.reset')}
                </Button>
              </div>
            </div>
          </div>

          <Table
            columns={columns}
            data={displayedProducts}
            emptyMessage={t('demo.list.emptyMessage')}
          />

          <div className="mt-4 flex flex-col md:flex-row items-center justify-between rtl-text-right text-left">
            <div className="flex items-center space-x-2 rtl-space-x-reverse mb-4 md:mb-0">
              <span className="text-sm text-gray-500">{t('demo.list.show')}</span>
              <select
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
                className="border border-gray-300 rounded-md text-sm p-1"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
              <span className="text-sm text-gray-500">{t('demo.list.perPage')}</span>
            </div>

            <div className="flex items-center rtl-flex-row-reverse">
              <span className="text-sm text-gray-500 mr-4 rtl-mr-0 rtl-ml-4">
                {t('demo.list.showing')} {filteredProducts.length > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0} {t('demo.list.to')}{' '}
                {Math.min(currentPage * itemsPerPage, filteredProducts.length)} {t('demo.list.of')}{' '}
                {filteredProducts.length} {t('demo.list.results')}
              </span>

              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default ListDemo;
