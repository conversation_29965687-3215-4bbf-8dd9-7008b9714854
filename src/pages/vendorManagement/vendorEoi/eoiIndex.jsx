import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { FaEdit, FaEye, FaHandshake } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";

const eoiIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: eoiData,
    isLoading,
    isError: eoiError,
    refetch,
  } = fetchData("general/vendor-eoi", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    // ...filterOptions,
  });

  const eoiList = eoiData?.data?.data || [];
  const paginationInfo = {
    currentPage: eoiData?.data?.current_page || 1,
    perPage: eoiData?.data?.per_page || itemsPerPage,
    totalItems: eoiData?.data?.total_items || 0,
    totalPages: eoiData?.data?.total_pages || 1,
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handlePreviewClick = (row) => {
    navigate(`/vendor/eoi/preview/${row.id}`);
  };

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name_tl_en",
    },
    {
      header: t("commonTableLabel.name_ar"),
      accessor: "name_tl_ar",
    },
    {
      header: t("commonTableLabel.approval_status"),
      accessor: "approval_status",
      render: (row) => {
        const statusColors = {
          Pending: "bg-red-100 text-red-600",
          Approved: "bg-green-100 text-green-600",
        };

        return (
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
              statusColors[row.approval_status]
            }`}
          >
            {row.approval_status.charAt(0).toUpperCase() +
              row.approval_status.slice(1)}
          </span>
        );
      },
    },

    // {
    //   header: t("commonTableLabel.status"),
    //   accessor: "is_active",
    //   render: (row) => {
    //     const isActive = row.is_active;
    //     return (
    //       <span
    //         className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
    //           isActive
    //             ? "bg-green-100 text-green-800"
    //             : "bg-gray-100 text-gray-600"
    //         }`}
    //       >
    //         {isActive
    //           ? t("commonOptions.status.active")
    //           : t("commonOptions.status.inactive")}
    //       </span>
    //     );
    //   },
    // },

    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title={t("commonTableLabel.previewAction")}
          >
            <FaEye className="text-blue-600" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="max-w-7xl mx-auto relative pt-6">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("vendor.title")}
          icon={<FaHandshake className="text-indigo-600 me-1" />}
        >
          <Table
            columns={columns}
            data={eoiList}
            emptyMessage={t("vendor.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>
    </div>
  );
};

export default eoiIndex;
