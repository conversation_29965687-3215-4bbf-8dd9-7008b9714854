import { useState, useEffect } from "react";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";
import { useDebounce } from "@/hooks/useDebounce";
import { useTranslation } from "react-i18next";

const dropDownFilters = ({ onChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: "",
    status: true,
  });

  const debouncedSearchTerm = useDebounce(filters.search, 500);

  useEffect(() => {
    if (onChange) {
      onChange({ search: debouncedSearchTerm, status: filters.status });
    }
  }, [debouncedSearchTerm, filters.status]);

  // Filter options
  const statusOptions = [
    { label: "Published", value: "published" },
    { label: "Draft", value: "draft" },
  ];

  const handleSearchChange = (value) => {
    setFilters((prev) => ({ ...prev, search: value }));
  };

  const handleFilterChange = (filterType, values) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: values,
    }));
    if (onChange) {
      onChange({ [filterType]: values });
    }
  };

  const handleReset = () => {
    setFilters({
      search: "",
      status: true,
    });
    if (onChange) {
      onChange({ search: "", status: true });
    }
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder={t("dropdownMenu.searchPlaceholder")}
          />
        </div>

        <div className="flex flex-wrap items-center space-x-2">
          {/* <FilterDropdown
            label="Status"
            options={statusOptions}
            selectedValues={filters.status}
            onChange={(values) => handleFilterChange("status", values)}
          /> */}

          <Button variant="outline" onClick={handleReset} className="ml-2">
            {t("commonButton.reset")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default dropDownFilters;
