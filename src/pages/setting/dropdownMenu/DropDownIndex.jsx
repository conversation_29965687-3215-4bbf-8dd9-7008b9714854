import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import DropDownFilter from "./dropDownFilters";
import { FaEdit, FaTrash, FaPlus, FaBars } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";

const DropDownIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedDropDown, setSelectedDropDown] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: true,
  });

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: dropDownData,
    isLoading,
    isError: dropDownError,
    refetch,
  } = fetchData("admin/dropdowns", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    ...filterOptions,
  });

  const dropDownList = dropDownData?.data?.data || [];
  const paginationInfo = {
    currentPage: dropDownData?.data?.current_page || 1,
    perPage: dropDownData?.data?.per_page || itemsPerPage,
    totalItems: dropDownData?.data?.total_items || 0,
    totalPages: dropDownData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditDropDown = (dropDown) => {
    navigate(`/setting/menu/dropdown/edit/${dropDown.id}`);
  };

  const handleDeleteClick = (dropDown) => {
    setSelectedDropDown(dropDown);
  };

  const handleDeleteDropDown = async () => {
    if (!selectedDropDown) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/dropdowns/${selectedDropDown.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedDropDown(null);
          refetch();
          toast.success(t("commonToast.dropDownToast.dropDownDelete"));
        },
        onError: (error) => {
          console.error(
            "= deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "DropDown deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditDropDown = (dropDown) => true;

  const canDeleteDropDown = (dropDown) => true;

  // Table columns configuration
  const columns = [
    {
      header: t("commonTableLabel.id"),
      accessor: "id",
    },
    {
      header: t("commonTableLabel.name_en"),
      accessor: "name_en",
    },
    {
      header: t("commonTableLabel.name_ar"),
      accessor: "name_ar",
    },
    {
      header: t("commonTableLabel.actions"),
      render: (row) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditDropDown(row)}
            disabled={!canEditDropDown(row)}
            title={
              !canEditDropDown(row)
                ? "You don't have permission to edit this DropDown Menu"
                : t("dropdownMenu.editAction")
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteDropDown(row)}
            title={
              !canDeleteDropDown(row)
                ? "You can't delete this DropDown Menu"
                : t("dropdownMenu.deleteAction")
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="max-w-7xl mx-auto relative pt-6">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={t("dropdownMenu.title")}
          icon={<FaBars className="text-indigo-600 me-1" />}
          action={
            <Button
              variant="primary"
              className="gap-2"
              onClick={() => navigate("/setting/menu/dropdown/add")}
            >
              <FaPlus className="mr-2" /> {t("dropdownMenu.add")}
            </Button>
          }
        >
          <DropDownFilter onChange={handleFilterChange} />

          <Table
            columns={columns}
            data={dropDownList}
            emptyMessage={t("dropdownMenu.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      {selectedDropDown && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">
              {t("commonDelete.deleteTitle")}
            </h3>
            <p className="text-gray-700 mb-6">
              {t("commonDelete.deleteMessage")}{" "}
              <span className="font-bold mx-1">DropDown</span>
              <span className="font-semibold">{selectedDropDown?.name_en}</span>
              ? {t("commonDelete.deleteWarning")}
            </p>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setSelectedDropDown(null)}
              >
                {t("commonButton.cancel")}
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteDropDown}
                disabled={deleteLoading}
              >
                {deleteLoading ? (
                  <LoadingSpinner size={20} />
                ) : (
                  t("commonButton.delete")
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DropDownIndex;
