import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import {
  FormInput,
  FormSelect,
  FormRadioGroup,
} from "../../components/ui/form";
import Button from "../../components/ui/Button";
import {
  getAllRoles,
  getRoleDisplayName,
  rolePermissions,
} from "../../constants/roles";
import PermissionManager from "./PermissionManager";

const UserEditForm = ({ user, onSubmit, onCancel }) => {
  const { user: currentUser } = useSelector((state) => state.auth);
  const [customPermissions, setCustomPermissions] = useState([]);
  const [selectedRole, setSelectedRole] = useState(user ? user.role : "viewer");

  // Determine available roles based on current user's role
  const availableRoles = getAllRoles().filter((role) => {
    // Super admin can assign any role
    if (currentUser.role === "super_admin") return true;
    // Admin can't assign super admin role
    if (currentUser.role === "admin" && role.key === "super_admin")
      return false;
    // Others can only assign roles with less privilege
    return true;
  });

  // Initialize custom permissions based on user's role
  useEffect(() => {
    if (user && user.permissions) {
      // setCustomPermissions(user.permissions);
      setCustomPermissions();
    } else if (selectedRole) {
      // setCustomPermissions(rolePermissions[selectedRole] || []);
      setCustomPermissions();
    }
  }, [user, selectedRole]);

  // Handle role change
  const handleRoleChange = (role) => {
    setSelectedRole(role);
    // Reset permissions to role defaults when role changes
    // setCustomPermissions(rolePermissions[role] || []);
    setCustomPermissions();
  };

  // Initial values for the form
  const initialValues = user
    ? {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        password: "",
        password_confirmation: "",
        phone: user.phone || "",
        // permissions: user.permissions || rolePermissions[user.role] || [],
        // permissions: [],
      }
    : {
        name: "",
        email: "",
        role: "viewer", // Default role
        status: "active", // Default status
        password: "",
        password_confirmation: "",
        phone: " ",
        // permissions: rolePermissions["viewer"] || [],
        // permissions: [],
      };

  // Validation schema
  const validationSchema = Yup.object({
    name: Yup.string().required("Name is required"),
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    // role: Yup.string().required("Role is required"),
    status: Yup.string().required("Status is required"),
    password: user
      ? Yup.string() // Optional for existing users
      : Yup.string()
          .min(8, "Password must be at least 8 characters")
          .required("Password is required"),
    password_confirmation: user
      ? Yup.string().oneOf([Yup.ref("password"), null], "Passwords must match")
      : Yup.string()
          .oneOf([Yup.ref("password"), null], "Passwords must match")
          .required("Confirm password is required"),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    const data = { ...values };
    if (user && !data.password) {
      delete data.password;
      delete data.password_confirmation;
    }
    onSubmit(data);
    setSubmitting(false);
  };

  // Handle permission changes
  const handlePermissionChange = (permissions) => {
    // setCustomPermissions(permissions);
  };

  // Role options
  const roleOptions = availableRoles.map((role) => ({
    label: getRoleDisplayName(role.key),
    value: role.key,
  }));

  // Status options
  const statusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
    { label: "Suspended", value: "suspended" },
  ];

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ isSubmitting, values, setFieldValue }) => (
        <Form className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormInput
              name="name"
              label="Name"
              placeholder="John Doe"
              required
            />

            <FormInput
              name="phone"
              label="Phone"
              placeholder="+088"
              // required
            />

            <FormInput
              name="email"
              type="email"
              label="Email Address"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormSelect
              name="role"
              label="Role"
              options={roleOptions}
              required
              disabled={
                user &&
                user.role === "super_admin" &&
                currentUser.role !== "super_admin"
              }
              onChange={(e) => {
                handleRoleChange(e.target.value);
                // Also update the Formik field
                setFieldValue("role", e.target.value);
              }}
            />

            <FormRadioGroup
              name="status"
              label="Status"
              options={statusOptions}
              required
              disabled={
                user &&
                user.role === "super_admin" &&
                currentUser.role !== "super_admin"
              }
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormInput
              name="password"
              type="password"
              label={
                user ? "New Password (leave blank to keep current)" : "Password"
              }
              placeholder="••••••••"
              required={!user}
            />

            <FormInput
              name="password_confirmation"
              type="password"
              label="Confirm Password"
              placeholder="••••••••"
              required={!user}
            />
          </div>

          {/* Permission Manager */}
          {/* <div className="border-t border-gray-200 pt-6 mt-6">
            <PermissionManager
              userPermissions={customPermissions}
              onChange={handlePermissionChange}
              userRole={values.role}
            />
          </div> */}

          <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-6">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>

            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {user ? "Update User" : "Create User"}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default UserEditForm;
