import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { motion } from "framer-motion";
import {
  FaUserPlus,
  FaEdit,
  FaTrash,
  FaUserShield,
  <PERSON>a<PERSON><PERSON>,
} from "react-icons/fa";
import Card from "../../components/ui/Card";
import Table from "../../components/ui/Table";
import Pagination from "../../components/ui/Pagination";
import SearchInput from "../../components/ui/SearchInput";
import FilterDropdown from "../../components/ui/FilterDropdown";
import Button from "../../components/ui/Button";
import Modal from "../../components/ui/Modal";
import { filterUsers, paginateUsers } from "../../utils/mockUsers";
import { updateUser, deleteUser, addUser } from "../../store/slices/usersSlice";
import {
  getAllRoles,
  getRoleDisplayName,
} from "../../constants/roles";
import UserEditForm from "./UserEditForm";
//call api service
import apiService from "../../services/apiService";

/**
 * Get all permissions from localStorage.
 * @returns {Array}
 */
const getAllPermissions = () => {
  try {
    const perms = localStorage.getItem('all_permissions');
    return perms ? JSON.parse(perms) : [];
  } catch {
    return [];
  }
};

const UserManagement = () => {
  const dispatch = useDispatch();

  const [users, setUsers] = useState([]);
  const [currentUser, setCurrentUser] = useState({ id: 1, role: "admin" });

  // const { users } = useSelector((state) => state.users);
  // const { user: currentUser } = useSelector((state) => state.auth);

  // State for filtered and paginated users
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [displayedUsers, setDisplayedUsers] = useState([]);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    roles: [],
    statuses: [],
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await apiService("admin/users", "GET");
        const userList = response?.data?.data || [];
        setUsers(userList);
        setFilteredUsers(userList);
      } catch (error) {
        console.error("Failed to fetch users", error);
      }
    };
    fetchUsers();
  }, []);

  // Apply filters and search when they change
  useEffect(() => {
    const filtered = filterUsers(users, searchTerm, filters);
    setFilteredUsers(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [users, searchTerm, filters]);

  // Apply pagination when filtered users or current page changes
  useEffect(() => {
    const paginated = paginateUsers(filteredUsers, currentPage, itemsPerPage);
    setDisplayedUsers(paginated);
  }, [filteredUsers, currentPage, itemsPerPage]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search change
  const handleSearchChange = (value) => {
    setSearchTerm(value);
  };

  // Handle filter change
  const handleFilterChange = (filterType, values) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: values,
    }));
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1); // Reset to first page when items per page changes
  };

  // Reset all filters
  const handleResetFilters = () => {
    setSearchTerm("");
    setFilters({
      roles: [],
      statuses: [],
    });
  };

  // Open edit modal
  const handleEditUser = (user) => {
    setSelectedUser(user);
    setEditModalOpen(true);
  };

  // Open delete modal
  const handleDeleteClick = (user) => {
    setSelectedUser(user);
    setDeleteModalOpen(true);
  };

  // Handle user update
  // const handleUpdateUser = (updatedUser) => {
  //   if (!updatedUser.id) {
  //     const newUser = {
  //       ...updatedUser,
  //       id: Math.max(...users.map((u) => u.id), 0) + 1,
  //       createdAt: new Date().toISOString(),
  //       lastLogin: null,
  //     };
  //     dispatch(addUser(newUser));
  //   } else {

  //     dispatch(updateUser(updatedUser));
  //   }
  //   setEditModalOpen(false);
  // };

  // Handle user delete
  const handleDeleteUser = () => {
    if (selectedUser) {
      dispatch(deleteUser(selectedUser.id));
      setDeleteModalOpen(false);
    }
  };

  const handleUpdateUser = async (updatedUser) => {
    try {
      const dataToSend = { ...updatedUser };
      delete dataToSend.confirmPassword;

      if (updatedUser.id && !dataToSend.password) {
        delete dataToSend.password;
      }

      if (!updatedUser.id) {
        const response = await apiService("admin/users", "POST", dataToSend);
        const newUser = response?.data;
        setUsers((prev) => [...prev, newUser]);
      } else {
        const response = await apiService(
          `admin/users/${updatedUser.id}`,
          "PUT",
          dataToSend
        );
        const updated = response?.data;
        setUsers((prev) =>
          prev.map((u) => (u.id === updated.id ? updated : u))
        );
      }

      setEditModalOpen(false);
    } catch (error) {
      console.error(
        "User update failed:",
        error?.response?.data || error.message
      );
      alert(
        "User update failed: " +
          (error?.response?.data?.message || error.message)
      );
    }
  };

  // Check if user can be edited (can't edit super admin if you're not super admin)
  const canEditUser = (user) => {
    if (currentUser.role === "super_admin") return true;
    return user.role !== "super_admin";
  };

  // Check if user can be deleted (can't delete yourself or super admin if you're not super admin)
  const canDeleteUser = (user) => {
    if (user.id === currentUser.id) return false;
    if (currentUser.role === "super_admin") return true;
    return user.role !== "super_admin";
  };

  // Table columns configuration
  const columns = [
    {
      header: "ID",
      accessor: "id",
    },
    {
      header: "Name",
      accessor: "name",
    },
    {
      header: "Phone",
      accessor: "phone",
    },
    {
      header: "Email",
      accessor: "email",
    },
    {
      header: "Role",
      accessor: "roles",
      render: (row) => row.roles?.[0]?.name || "N/A",
    },

    {
      header: "Verified",
      accessor: "is_verified",
      render: (row) => (
        <span
          className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
            row.is_verified
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {row.is_verified ? "Yes" : "No"}
        </span>
      ),
    },

    // {
    //   header: 'Status',
    //   accessor: 'status',
    //   render: (row) => {
    //     const statusColors = {
    //       'active': 'bg-green-100 text-green-800',
    //       'inactive': 'bg-gray-100 text-gray-800',
    //       'suspended': 'bg-red-100 text-red-800',
    //     };

    //     return (
    //       <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[row.status]}`}>
    //         {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
    //       </span>
    //     );
    //   },
    // },
    // {
    //   header: 'Last Login',
    //   accessor: 'lastLogin',
    //   render: (row) => row.lastLogin
    //     ? new Date(row.lastLogin).toLocaleDateString()
    //     : 'Never',
    // },
    {
      header: "Actions",
      render: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditUser(row)}
            disabled={!canEditUser(row)}
            title={
              !canEditUser(row)
                ? "You don't have permission to edit this user"
                : "Edit user"
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteUser(row)}
            title={
              !canDeleteUser(row) ? "You can't delete this user" : "Delete user"
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
          {row.permissions && (
            <div className="relative group">
              <Button
                variant="outline"
                size="sm"
                title="User has custom permissions"
                className="cursor-default"
              >
                <FaKey
                  className={
                    row.permissions.length !== getAllPermissions().length
                      ? "text-yellow-500"
                      : "text-gray-400"
                  }
                />
              </Button>
              <div className="absolute z-10 hidden group-hover:block bg-white border border-gray-200 rounded-md shadow-lg p-2 w-48 text-xs right-0 mt-1">
                <p className="font-semibold mb-1">Custom Permissions:</p>
                <p className="text-gray-600">
                  {row.permissions &&
                  row.permissions.length !== getAllPermissions().length
                    ? `${row.permissions.length} permissions (${
                        getAllPermissions().length || 0
                      } in role)`
                    : "Standard role permissions"}
                </p>
              </div>
            </div>
          )}
        </div>
      ),
    },
  ];

  // Filter options
  const roleOptions = getAllRoles().map((role) => ({
    label: getRoleDisplayName(role.key),
    value: role.key,
  }));

  const statusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
    { label: "Suspended", value: "suspended" },
  ];

  // Calculate total pages
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);

  return (
    <div className="max-w-7xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title="User Management"
          icon={<FaUserShield className="text-indigo-600" />}
        >
          <div className="mb-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
              <div className="flex-1 max-w-md">
                <SearchInput
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search users..."
                />
              </div>

              <div className="flex flex-wrap items-center space-x-2">
                <FilterDropdown
                  label="Role"
                  options={roleOptions}
                  selectedValues={filters.roles}
                  onChange={(values) => handleFilterChange("roles", values)}
                />

                <FilterDropdown
                  label="Status"
                  options={statusOptions}
                  selectedValues={filters.statuses}
                  onChange={(values) => handleFilterChange("statuses", values)}
                />

                <Button
                  variant="outline"
                  onClick={handleResetFilters}
                  className="ml-2"
                >
                  Reset
                </Button>

                <Button
                  variant="primary"
                  className="ml-2"
                  onClick={() => {
                    setSelectedUser(null);
                    setEditModalOpen(true);
                  }}
                >
                  <FaUserPlus className="mr-2" /> Add User
                </Button>
              </div>
            </div>
          </div>

          <Table
            columns={columns}
            data={displayedUsers}
            emptyMessage="No users found matching your criteria."
          />

          <div className="mt-4 flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <span className="text-sm text-gray-500">Show</span>
              <select
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
                className="border border-gray-300 rounded-md text-sm p-1"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
              <span className="text-sm text-gray-500">per page</span>
            </div>

            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-4">
                Showing{" "}
                {filteredUsers.length > 0
                  ? (currentPage - 1) * itemsPerPage + 1
                  : 0}{" "}
                to {Math.min(currentPage * itemsPerPage, filteredUsers.length)}{" "}
                of {filteredUsers.length} results
              </span>

              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Edit User Modal */}
      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={selectedUser ? "Edit User" : "Add New User"}
        size="lg"
      >
        <UserEditForm
          user={selectedUser}
          onSubmit={handleUpdateUser}
          onCancel={() => setEditModalOpen(false)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Confirm Delete"
        size="sm"
      >
        <div className="py-4">
          <p className="text-gray-700">
            Are you sure you want to delete the user{" "}
            <span className="font-semibold">{selectedUser?.name}</span>? This
            action cannot be undone.
          </p>
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeleteUser}>
            Delete
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default UserManagement;
