import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

/**
 * Permission Manager Component
 * Allows adding and removing individual permissions from a user
 * 
 * @param {Object} props - Component props
 * @param {Array} props.userPermissions - Array of user permissions
 * @param {Function} props.onChange - Function to call when permissions change
 * @param {string} props.defaultPermissions - Default permissions, these will be fixed and disabled
 * @returns {JSX.Element} - Permission Manager component
 */
const PermissionManager = ({ userPermissions = [], onChange, defaultPermissions = [], permissions = [] }) => {
  // Group permissions by category
  const [permissionGroups, setPermissionGroups] = useState({});
  const [selectedPermissions, setSelectedPermissions] = useState(new Set(userPermissions));
  const [defaultPermission, setDefaultPermissions] = useState(new Set(defaultPermissions))

  // Extract permission category from permission object (e.g., group_name)
  const getPermissionCategory = (permission) => {
    // Prefer group_name if present, else fallback to splitting name
    if (permission.group_name) return permission.group_name;
    if (permission.name && permission.name.includes(':')) {
      return permission.name.split(':')[0];
    }
    return 'other';
  };

  // Group permissions by category using dynamic permissions prop
  useEffect(() => {
    const groups = {};

    permissions.forEach((perm) => {
      const category = getPermissionCategory(perm);

      if (!groups[category]) {
        groups[category] = [];
      }

      groups[category].push({
        key: perm.name,
        value: perm.name,
        label: perm.name
          .replace(/_/g, ' ')
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
      });
    });

    // Sort categories alphabetically
    const sortedGroups = {};
    Object.keys(groups).sort().forEach(key => {
      sortedGroups[key] = groups[key];
    });

    setPermissionGroups(sortedGroups);
  }, [permissions]);
  
  // Initialize selected permissions, excluding default permissions
  useEffect(() => {
    const filteredPermissions = userPermissions.filter(
      perm => !defaultPermissions.includes(perm)
    );
    setSelectedPermissions(new Set(filteredPermissions));
  }, [userPermissions, defaultPermissions]);

  useEffect(() => {
    setDefaultPermissions(new Set(defaultPermissions))
  }, [defaultPermissions])
  
  // Handle permission toggle
  const handlePermissionToggle = (permission) => {
    const newPermissions = new Set(selectedPermissions);
    
    if (newPermissions.has(permission)) {
      newPermissions.delete(permission);
    } else {
      newPermissions.add(permission);
    }
    
    setSelectedPermissions(newPermissions);
    onChange(Array.from(newPermissions));
  };
  
  // Get role default permissions
  const getRoleDefaultPermissions = () => {
    return defaultPermissions || [];
  };
  
  // Check if permission is included in role defaults
  const isRoleDefault = (permission) => {
    return getRoleDefaultPermissions().includes(permission);
  };
  
  // Reset permissions to empty (since defaults are handled separately)
  const handleResetToDefaults = () => {
    setSelectedPermissions(new Set());
    onChange([]);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">User Permissions</h3>
        <button
          type="button"
          onClick={handleResetToDefaults}
          className="text-sm text-indigo-600 hover:text-indigo-800"
        >
          Reset to Role Defaults
        </button>
      </div>
      
      <div className="text-sm text-gray-500 mb-4">
        <p>Customize this user's permissions beyond their role defaults.</p>
        <p className="mt-1">
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
            Default
          </span>
          {' '}permissions are included in the user's role.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-h-96 overflow-y-auto">
        {Object.entries(permissionGroups).map(([category, permissions]) => (
          <motion.div
            key={category}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
            className="border border-gray-200 rounded-md p-4"
          >
            <h4 className="font-medium text-gray-900 capitalize mb-3">{category?.replace(/_/g, ' ')}</h4>
            <div className="space-y-2">
              {permissions.map((permission) => (
                <div key={permission.value} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`permission-${permission.key}`}
                    checked={selectedPermissions.has(permission.value) || defaultPermission.has(permission.value)}
                    onChange={() => handlePermissionToggle(permission.value)}
                    disabled={defaultPermission.has(permission.value)}
                    className={`h-4 w-4 ${defaultPermission.has(permission.value) ? 'text-gray-400' : 'text-indigo-600'} focus:ring-indigo-500 border-gray-300 rounded`}
                  />
                  <label
                    htmlFor={`permission-${permission.key}`}
                    className="ml-2 block text-sm text-gray-700"
                  >
                    {permission.label}
                    {isRoleDefault(permission.value) && (
                      <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        Default
                      </span>
                    )}
                  </label>
                </div>
              ))}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default PermissionManager;
