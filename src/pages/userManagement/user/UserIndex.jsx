import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import {
  FaUserPlus,
  FaEdit,
  FaTrash,
  FaUserShield,
  FaKey,
} from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { rolePermissions } from "@/constants/roles";
import UserEditForm from "./UserAddEdit";
import UserFilters from "./UserFilters";
//call api service
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";

const UserIndex = () => {
  const { postForm, putForm, delete: removeUser } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // Filter state
  const [filterOptions, setFilterOptions] = useState({
    search: "",
    roles: [],
    statuses: []
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const {
    data: usersData,
    isLoading: usersLoading,
    isError: usersError,
    refetch: refetchUsers,
  } = fetchData("admin/users", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search,
    roles: filterOptions.roles,
    statuses: filterOptions.statuses
  });

  const userList = usersData?.data?.data || [];
  const paginationInfo = {
    currentPage: usersData?.data?.current_page || 1,
    perPage: usersData?.data?.per_page || itemsPerPage,
    totalItems: usersData?.data?.total_items || 0,
    totalPages: usersData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setEditModalOpen(true);
  };

  const handleDeleteClick = (user) => {
    setSelectedUser(user);
    setDeleteModalOpen(true);
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;
    setDeleteLoading(true);
    removeUser(`admin/users/${selectedUser.id}`)
      .then(() => {
        setDeleteModalOpen(false);
        setSelectedUser(null);
        refetchUsers();
        toast.success("User deleted successfully.");
      })
      .finally(setDeleteLoading(false));
  };

  // Use postMutation and putMutation for user add/update
  const [editLoading, setEditLoading] = useState(false);

  const handleUpdateUser = (formData) => {
    setEditLoading(true);

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedUser(null);
      refetchUsers();
      toast.success(
        `User ${formData.get("id") ? "updated" : "created"} successfully.`
      );
    };

    const onSettled = () => setEditLoading(false);

    if (!formData.get("id")) {
      postForm(`admin/users`, formData).then(onSuccess).finally(onSettled);
    } else {
      putForm(`admin/users/${formData.get("id")}`, formData)
        .then(onSuccess)
        .finally(onSettled);
    }
  };

  // Table columns configuration
  const columns = [
    {
      header: "ID",
      accessor: "id",
    },
    {
      header: "Name",
      accessor: "name",
    },
    {
      header: "Phone",
      accessor: "phone",
    },
    {
      header: "Email",
      accessor: "email",
    },
    {
      header: "Role",
      accessor: "roles",
      render: (row) => row.roles?.[0]?.name || "N/A",
    },

    {
      header: "Verified",
      accessor: "is_verified",
      render: (row) => (
        <span
          className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
            row.is_verified
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {row.is_verified ? "Yes" : "No"}
        </span>
      ),
    },

    {
      header: "Status",
      accessor: "status",
      render: (row) => {
        const statusColors = {
          active: "bg-green-100 text-green-800",
          inactive: "bg-gray-100 text-gray-800",
          suspended: "bg-red-100 text-red-800",
        };

        return (
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
              statusColors[row.status]
            }`}
          >
            {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
          </span>
        );
      },
    },
    {
      header: "Actions",
      render: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditUser(row)}
            // disabled={!canEditUser(row)}
            // title={
            //   !canEditUser(row)
            //     ? "You don't have permission to edit this user"
            //     : "Edit user"
            // }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            // disabled={!canDeleteUser(row)}
            // title={
            //   !canDeleteUser(row) ? "You can't delete this user" : "Delete user"
            // }
          >
            <FaTrash className="text-red-600" />
          </Button>
          {row.permissions && (
            <div className="relative group">
              <Button
                variant="outline"
                size="sm"
                title="User has custom permissions"
                className="cursor-default"
              >
                <FaKey
                  className={
                    row.permissions.length !== rolePermissions[row.role]?.length
                      ? "text-yellow-500"
                      : "text-gray-400"
                  }
                />
              </Button>
              <div className="absolute z-10 hidden group-hover:block bg-white border border-gray-200 rounded-md shadow-lg p-2 w-48 text-xs right-0 mt-1">
                <p className="font-semibold mb-1">Custom Permissions:</p>
                <p className="text-gray-600">
                  {row.permissions &&
                  row.permissions.length !== rolePermissions[row.role]?.length
                    ? `${row.permissions.length} permissions (${
                        rolePermissions[row.role]?.length || 0
                      } in role)`
                    : "Standard role permissions"}
                </p>
              </div>
            </div>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="max-w-7xl mx-auto relative pt-6">
      {usersLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title="User List"
          icon={<FaUserShield className="text-indigo-600" />}
          action={
            <Button
              variant="primary"
              className="mt-4"
              onClick={() => {
                setSelectedUser(null);
                setEditModalOpen(true);
              }}
            >
              <FaUserPlus className="mr-2" /> Add User
            </Button>
          }
        >
          <UserFilters
            onChange={handleFilterChange}
          />

          <Table
            columns={columns}
            data={userList}
            emptyMessage="No users found matching your criteria."
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>

      {/* Edit User Modal */}
      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={selectedUser ? "Edit User" : "Add New User"}
        size="xl"
      >
        <UserEditForm
          user={selectedUser}
          onSubmit={handleUpdateUser}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Confirm Delete"
        size="sm"
      >
        <div className="py-4">
          <p className="text-gray-700">
            Are you sure you want to delete the user{" "}
            <span className="font-semibold">{selectedUser?.name}</span>? This
            action cannot be undone.
          </p>
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteUser}
            disabled={deleteLoading}
          >
            {deleteLoading ? <LoadingSpinner size={20} /> : "Delete"}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default UserIndex;
