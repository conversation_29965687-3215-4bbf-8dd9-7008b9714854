import { useState, useEffect } from "react";
import { useDebounce } from "@/hooks/useDebounce";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";
import { getAllRoles, getRoleDisplayName } from "@/constants/roles";

const PermissionFilters = ({ onChange }) => {
  const [filters, setFilters] = useState({
    search: "",
    roles: [],
    statuses: []
  });

  const debouncedSearchTerm = useDebounce(filters.search, 500);

  useEffect(() => {
    if (onChange) {
      onChange({
        search: debouncedSearchTerm,
        roles: filters.roles,
        statuses: filters.statuses
      });
    }
  }, [debouncedSearchTerm, filters.roles, filters.statuses]);

  const roleOptions = getAllRoles().map((role) => ({
    label: getRoleDisplayName(role.key),
    value: role.key,
  }));

  const statusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
    { label: "Suspended", value: "suspended" },
  ];

  const handleSearchChange = (value) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  const handleFilterChange = (filterType, values) => {
    setFilters(prev => ({ ...prev, [filterType]: values }));
  };

  const handleReset = () => {
    setFilters({
      search: "",
      roles: [],
      statuses: []
    });
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder="Search permissions..."
          />
        </div>

        <div className="flex flex-wrap items-center space-x-2">
          {/* <FilterDropdown
            label="Role"
            options={roleOptions}
            selectedValues={filters.roles}
            onChange={(values) => handleFilterChange("roles", values)}
          />

          <FilterDropdown
            label="Status"
            options={statusOptions}
            selectedValues={filters.statuses}
            onChange={(values) => handleFilterChange("statuses", values)}
          /> */}

          <Button
            variant="outline"
            onClick={handleReset}
            className="ml-2"
          >
            Reset
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PermissionFilters;