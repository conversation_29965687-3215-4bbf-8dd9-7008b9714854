import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  FaUserPlus,
  FaEdit,
  FaTrash,
  FaUserShield
} from "react-icons/fa";
import Card from "../../../components/ui/Card";
import Table from "../../../components/ui/Table";
import PaginationInfo from "../../../components/ui/PaginationInfo";
import Button from "../../../components/ui/Button";
import Modal from "../../../components/ui/Modal";
import LoadingSpinner from "../../../components/ui/LoadingSpinner";
import PermissionEditForm from "./PermissionAddEdit";
import PermissionFilters from "./PermissionFilters";
import { fetchData, useApi } from "../../../hooks/useApi";
import usePagination from "../../../hooks/usePagination";
import { toast } from "sonner";

const PermissionIndex = () => {
  const { deleteMutation, postMutation, putMutation } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // Filter state
  const [filterOptions, setFilterOptions] = useState({
    search: "",
    roles: [],
    statuses: []
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedPermission, setSelectedPermission] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);


  const {
    data: permissionData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/permissions", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search,
    roles: filterOptions.roles,
    statuses: filterOptions.statuses
  });

  const permissionList = permissionData?.data?.data || [];
  const paginationInfo = {
    currentPage: permissionData?.data?.current_page || 1,
    perPage: permissionData?.data?.per_page || itemsPerPage,
    totalItems: permissionData?.data?.total_items || 0,
    totalPages: permissionData?.data?.total_pages || 1,
  };


  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  // Open edit modal
  const handleEditPermission = (permission) => {
    setSelectedPermission(permission);
    setEditModalOpen(true);
  };

  // Open delete modal
  const handleDeleteClick = (permission) => {
    setSelectedPermission(permission);
    setDeleteModalOpen(true);
  };

  const handleDeletePermission = async () => {
    if (!selectedPermission) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/permissions/${selectedPermission.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedPermission(null);
          refetch();
          toast.success("Permission deleted successfully.");
        },
        onError: (error) => {
          console.error(
            "Permission deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdatePermission = (updatedPermission) => {
    setEditLoading(true);
    const dataToSend = { ...updatedPermission };
    delete dataToSend.confirmPassword;

    if (updatedPermission.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedPermission(null);
      refetch();
      toast.success(`Permission ${updatedPermission.id ? "updated" : "created"} successfully.`);
    };
    const onError = (error) => {
      console.error(
        "Permission update failed:",
        error?.response?.data || error.message
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedPermission.id) {
      postMutation.mutate(
        { endpoint: "admin/permissions", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        { endpoint: `admin/permissions/${updatedPermission.id}`, data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    }
  };

  const canEditPermission = (permission) => true

  const canDeletePermission = (permission) => true

  // Table columns configuration
  const columns = [
    {
      header: "ID",
      accessor: "id",
    },
    {
      header: "Name",
      accessor: "name",
    },
    {
      header: "group_name",
      accessor: "group_name",
    },

    {
      header: "Actions",
      render: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditPermission(row)}
            disabled={!canEditPermission(row)}
            title={
              !canEditPermission(row)
                ? "You don't have permission to edit this permission"
                : "Edit permission"
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeletePermission(row)}
            title={
              !canDeletePermission(row) ? "You can't delete this permission" : "Delete permission"
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="max-w-7xl mx-auto relative">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title="Permission List"
          icon={<FaUserShield className="text-indigo-600" />}
          action={
            <Button
              variant="primary"
              onClick={() => {
                setSelectedPermission(null);
                setEditModalOpen(true);
              }}
            >
              <FaUserPlus className="mr-2" /> Add Permission
            </Button>
          }
        >
          <PermissionFilters
            onChange={handleFilterChange}
          />

          <Table
            columns={columns}
            data={permissionList}
            emptyMessage="No permissions found matching your criteria."
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>

      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={selectedPermission ? "Edit Permission" : "Add New Permission"}
        size="lg"
      >
        <PermissionEditForm
          permission={selectedPermission}
          onSubmit={handleUpdatePermission}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Confirm Delete"
        size="sm"
      >
        <div className="py-4">
          <p className="text-gray-700">
            Are you sure you want to delete the permission{" "}
            <span className="font-semibold">{selectedPermission?.name}</span>? This
            action cannot be undone.
          </p>
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeletePermission}
            disabled={deleteLoading}
          >
            {deleteLoading ? <LoadingSpinner size={20} /> : "Delete"}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default PermissionIndex;
