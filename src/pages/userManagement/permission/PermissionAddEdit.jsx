import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormInput } from "../../../components/ui/form";
import Button from "../../../components/ui/Button";

const PermissionAddEdit = ({ permission, onSubmit, onCancel }) => {
  const initialValues = permission
    ? {
        id: permission.id,
        name: permission.name || "",
        group: permission.group_name || "",
      }
    : {
        name: "",
        group: "",
      };

  const validationSchema = Yup.object({
    name: Yup.string().required("Name is required"),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    onSubmit(values);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting }) => (
        <Form className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormInput
              name="name"
              label="Name"
              placeholder="Role name"
              required
            />
            <FormInput
              name="group"
              label="Group"
              placeholder="Group name"
              required
            />
          </div>

          <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-6">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {permission ? "Update Permission" : "Create Permission"}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default PermissionAddEdit;
