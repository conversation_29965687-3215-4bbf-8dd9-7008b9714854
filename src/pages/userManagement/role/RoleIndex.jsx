import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  FaUserPlus,
  FaEdit,
  FaTrash,
  FaUserShield,
} from "react-icons/fa";
import Card from "../../../components/ui/Card";
import Table from "../../../components/ui/Table";
import PaginationInfo from "../../../components/ui/PaginationInfo";
import Button from "../../../components/ui/Button";
import Modal from "../../../components/ui/Modal";
import LoadingSpinner from "../../../components/ui/LoadingSpinner";
import RoleEditForm from "./RoleAddEdit";
import RoleFilters from "./RoleFilters";
import { fetchData, useApi } from "../../../hooks/useApi";
import usePagination from "../../../hooks/usePagination";
import { toast } from "sonner";

const RoleIndex = () => {
  const { deleteMutation, postMutation, putMutation } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // Filter state
  const [filterOptions, setFilterOptions] = useState({
    search: ""
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);

  const {
    data: roleData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/roles", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    search: filterOptions.search,
  });

  const roleList = roleData?.data?.data || [];
  const paginationInfo = {
    currentPage: roleData?.data?.current_page || 1,
    perPage: roleData?.data?.per_page || itemsPerPage,
    totalItems: roleData?.data?.total_items || 0,
    totalPages: roleData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  // Open edit modal
  const handleEditRole = (role) => {
    setSelectedRole(role);
    setEditModalOpen(true);
  };

  // Open delete modal
  const handleDeleteClick = (role) => {
    setSelectedRole(role);
    setDeleteModalOpen(true);
  };

  const handleDeleteRole = async () => {
    if (!selectedRole) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/roles/${selectedRole.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedRole(null);
          refetch();
          toast.success("Role deleted successfully.");
        },
        onError: (error) => {
          console.error(
            "Role deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdateRole = (updatedRole) => {
    setEditLoading(true);
    const dataToSend = { ...updatedRole };
    delete dataToSend.confirmPassword;

    if (updatedRole.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedRole(null);
      refetch();
      toast.success(`Role ${updatedRole.id ? "updated" : "created"} successfully.`);
    };
    const onError = (error) => {
      console.error(
        "update failed:",
        error?.response?.data || error.message
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedRole.id) {
      postMutation.mutate(
        { endpoint: "admin/roles", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        { endpoint: `admin/roles/${updatedRole.id}`, data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    }
  };

  const canEditRole = (role) => {
    return true
  };

  const canDeleteRole = (role) => {
    return true
  };

  // Table columns configuration
  const columns = [
    {
      header: "ID",
      accessor: "id",
    },
    {
      header: "Name",
      accessor: "name",
    },
    {
      header: "guard_name",
      accessor: "guard_name",
    },

    {
      header: "Actions",
      render: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditRole(row)}
            disabled={!canEditRole(row)}
            title={
              !canEditRole(row)
                ? "You don't have permission to edit this role"
                : "Edit role"
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteRole(row)}
            title={
              !canDeleteRole(row) ? "You can't delete this role" : "Delete role"
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="max-w-7xl mx-auto relative">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title="Role List"
          icon={<FaUserShield className="text-indigo-600" />}
          action={
            <Button
              variant="primary"
              className="mt-4"
              onClick={() => {
                setSelectedRole(null);
                setEditModalOpen(true);
              }}
            >
              <FaUserPlus className="mr-2" /> Add Role
            </Button>
          }
        >
          <RoleFilters
            onChange={handleFilterChange}
          />

          <Table
            columns={columns}
            data={roleList}
            emptyMessage="No roles found matching your criteria."
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>

      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={selectedRole ? "Edit Role" : "Add New Role"}
        size="xl"
      >
        <RoleEditForm
          role={selectedRole}
          onSubmit={handleUpdateRole}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Confirm Delete"
        size="sm"
      >
        <div className="py-4">
          <p className="text-gray-700">
            Are you sure you want to delete the role{" "}
            <span className="font-semibold">{selectedRole?.name}</span>? This
            action cannot be undone.
          </p>
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteRole}
            disabled={deleteLoading}
          >
            {deleteLoading ? <LoadingSpinner size={20} /> : "Delete"}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default RoleIndex;
