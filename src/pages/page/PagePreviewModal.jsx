import Button from "@/components/ui/Button";

const PagePreviewModal = ({ page, onClose }) => {
  return (
    <div className="flex-1 overflow-y-auto p-6 space-y-8">
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <span className="h-3 w-3 rounded-full bg-blue-500"></span>
          <h4 className="text-lg font-medium text-gray-900">English Content</h4>
        </div>
        <div
          className="prose max-w-none p-4 bg-gray-50 rounded-lg"
          dangerouslySetInnerHTML={{ __html: page?.content_en }}
        />
      </div>
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <span className="h-3 w-3 rounded-full bg-green-500"></span>
          <h4 className="text-lg font-medium text-gray-900">Arabic Content</h4>
        </div>
        <div
          className="prose max-w-none p-4 bg-gray-50 rounded-lg"
          dangerouslySetInnerHTML={{ __html: page?.content_ar }}
        />
      </div>
      <div className="p-4 border-t border-gray-200 flex justify-end">
        <Button
          variant="outline"
          onClick={onClose}
          className="px-6"
        >
          Close
        </Button>
      </div>
    </div>
  );
};

export default PagePreviewModal;