import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { FaEdit, FaTrash, FaKey, FaPlus, FaPager, FaEye } from "react-icons/fa";
import Card from "@/components/ui/Card";
import PagePreviewModal from "./PagePreviewModal";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import PageFilters from "./PageFilters";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import PageEditForm from "./PageEditUpdate";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";

const PageIndex = () => {
  const { deleteMutation: deletePageMutation, postMutation, putMutation } = useApi();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const [filterOptions, setFilterOptions] = useState({
    search: "",
    status: true
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [selectedPage, setSelectedPage] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);

  const {
    data: pageData,
    isLoading: pagesLoading,
    isError: pagesError,
    refetch: refetchPages,
  } = fetchData("admin/pages", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    ...filterOptions
  });

  // Extract page list and pagination info from API response
  const pageList = pageData?.data?.data || [];
  const paginationInfo = {
    currentPage: pageData?.data?.current_page || 1,
    perPage: pageData?.data?.per_page || itemsPerPage,
    totalItems: pageData?.data?.total_items || 0,
    totalPages: pageData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters);
    setCurrentPage(1)
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1)
  };

  const handleEditPage = (page) => {
    setSelectedPage(page);
    setEditModalOpen(true);
  };

  const handleDeleteClick = (page) => {
    setSelectedPage(page);
    setDeleteModalOpen(true);
  };

  const handlePreviewClick = (page) => {
    setSelectedPage(page);
    setPreviewModalOpen(true);
  };

  const handleDeletePage = async () => {
    if (!selectedPage) return;
    setDeleteLoading(true);
    deletePageMutation.mutate(
      {
        endpoint: `admin/pages/${selectedPage.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedPage(null);
          refetchPages();
          toast.success("Page deleted successfully.");
        },
        onError: (error) => {
          console.error(
            "Page deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdatePage = (updatedPage) => {
    setEditLoading(true);
    const dataToSend = { ...updatedPage };
    delete dataToSend.confirmPassword;

    if (updatedPage.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedPage(null);
      refetchPages();
      toast.success(`Page ${updatedPage.id ? "updated" : "created"} successfully.`);
    };
    const onError = (error) => {
      console.error(
        "Page update failed:",
        error?.response?.data || error.message
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedPage.id) {
      postMutation.mutate(
        { endpoint: "admin/pages", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        { endpoint: `admin/pages/${updatedPage.id}`, data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    }
  };

  // Check if page can be edited (can't edit super admin if you're not super admin)
  const canEditPage = (page) => true

  // Check if page can be deleted (can't delete yourself or super admin if you're not super admin)
  const canDeletePage = (page) => true

  const handleOnCreate = () => {
    setSelectedPage(null);
    setEditModalOpen(true);
  }
  // Table columns configuration
  const columns = [
    {
      header: "ID",
      accessor: "id",
    },
    {
      header: "Title (EN)",
      accessor: "title_en",
    },
    {
      header: "Title (AR)",
      accessor: "title_ar",
    },
    {
      header: "Slug",
      accessor: "slug",
    },
    {
      header: "Status",
      accessor: "status",
      render: (row) => {
        const statusColors = {
          active: "bg-green-100 text-green-800",
          inactive: "bg-red-100 text-red-600",
        };

        return (
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
              statusColors[row.status]
            }`}
          >
            {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
          </span>
        );
      },
    },

    {
      header: "Actions",
      render: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditPage(row)}
            disabled={!canEditPage(row)}
            title={
              !canEditPage(row)
                ? "You don't have permission to edit this page"
                : "Edit page"
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title="Preview page"
          >
            <FaEye className="text-blue-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            // disabled={!canDeletePage(row)}
            // title={
            //   !canDeletePage(row) ? "You can't delete this page" : "Delete page"
            // }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="max-w-7xl mx-auto relative">
      {pagesLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card title="Pages List" icon={<FaPager className="text-indigo-600" />}>
          <PageFilters
            onChange={handleFilterChange}
            onCreate={handleOnCreate}
          />

          <Table
            columns={columns}
            data={pageList}
            emptyMessage="No pages found matching your criteria."
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>

      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={selectedPage ? "Edit Pages" : "Add New Pages"}
        size="xl"
      >
        <PageEditForm
          page={selectedPage}
          onSubmit={handleUpdatePage}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Confirm Delete"
        size="sm"
      >
        <div className="py-4">
          <p className="text-gray-700">
            Are you sure you want to delete the pages{" "}
            <span className="font-semibold">{selectedPage?.title_en}</span>?
            This action cannot be undone.
          </p>
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeletePage}
            disabled={deleteLoading}
          >
            {deleteLoading ? <LoadingSpinner size={20} /> : "Delete"}
          </Button>
        </div>
      </Modal>

      <Modal
        isOpen={previewModalOpen}
        onClose={() => setPreviewModalOpen(false)}
        title={`Preview: ${selectedPage?.title_en}`}
        size="xl"
      >
        <PagePreviewModal
          page={selectedPage}
          onClose={() => setPreviewModalOpen(false)}
        />
      </Modal>
      </div>
  );
};

export default PageIndex;
