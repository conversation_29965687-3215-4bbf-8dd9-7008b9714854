import { useState, useEffect } from "react";
import { FaPlus } from "react-icons/fa";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";
import { useDebounce } from "@/hooks/useDebounce";

const PageFilters = ({
  onChange,
  onCreate
}) => {
  const [filters, setFilters] = useState({
    search: "",
    status: []
  });

  const debouncedSearchTerm = useDebounce(filters.search, 500);

  useEffect(() => {
    if (onChange) {
      onChange({ 
        search: debouncedSearchTerm, 
        status: filters.status 
      });
    }
  }, [debouncedSearchTerm, filters.status]);

  const statusOptions = [
    { label: "Active", value: [] },
    { label: "Inactive", value: false },
  ];

  const handleSearchChange = (value) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  const handleFilterChange = (filterType, values) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: values
    }));
  };

  const handleReset = () => {
    setFilters({
      search: "",
      status: []
    });
    if (onChange) {
      onChange({ search: "", status: [] });
    }
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
        <div className="flex-1 max-w-md">
          <SearchInput
            value={filters.search}
            onChange={handleSearchChange}
            placeholder="Search pages..."
          />
        </div>

        <div className="flex flex-wrap items-center space-x-2">
          {/* <FilterDropdown
            label="Status"
            options={statusOptions}
            selectedValues={filters.status}
            onChange={(values) => handleFilterChange("status", values)}
          /> */}

          <Button
            variant="outline"
            onClick={handleReset}
            className="ml-2"
          >
            Reset
          </Button>

          <Button
            variant="primary"
            className="ml-2"
            onClick={onCreate}
          >
            <FaPlus className="mr-2" /> Add Page
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PageFilters;