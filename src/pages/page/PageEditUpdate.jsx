import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useCallback } from "react";
import {
  FormInput,
  FormRadioGroup,
  RichTextEditor,
} from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { generateSlug } from "@/utils/slugUtils";

const PageEditUpdate = ({ page, onSubmit, onCancel, loading }) => {

  const initialValues = page
    ? {
        id: page.id,
        title_en: page.title_en || "",
        title_ar: page.title_ar || "",
        content_ar: page.content_ar || "",
        content_en: page.content_en || "",
        slug: page.slug || "",
        status: page.status || "active",
      }
    : {
        title_en: "",
        title_ar: "",
        content_ar: "",
        content_en: "",
        slug: "",
        status: "active",
      };

  const validationSchema = Yup.object({
    title_en: Yup.string().required("English Title is required"),
    title_ar: Yup.string().required("Arabic Title is required"),
    slug: Yup.string().required("Slug is required"),
    status: Yup.string().required("Status is required"),
  });

  const statusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
  ];

  const handleSubmit = (values, { setSubmitting }) => {
    onSubmit(values);
    setSubmitting(false);
  };

  const handleTitleChange = useCallback((e, setFieldValue) => {
    const newTitle = e.target.value;
    setFieldValue('title_en', newTitle);
    
    const timer = setTimeout(() => {
      const newSlug = generateSlug(newTitle);
      if (!page?.slug) {
        setFieldValue('slug', newSlug);
      }
    }, 300);
    
    return () => clearTimeout(timer);
  }, [page?.slug]);

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, values, setFieldValue }) => (
        <Form className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              name="title_en"
              label="Title in English"
              placeholder="Enter title in English"
              required
              onChange={(e) => handleTitleChange(e, setFieldValue)}
            />
            <FormInput
              name="title_ar"
              label="Title in Arabic"
              placeholder="Enter title in Arabic"
              required
            />
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 my-2">
                Content in English
              </label>
              <RichTextEditor
                name="content_en"
                value={values.content_en}
                onChange={(content) => setFieldValue("content_en", content)}
                placeholder="Enter Content in English"
              />
            </div>
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 my-2">
                Content in Arabic
              </label>
              <RichTextEditor
                name="content_ar"
                value={values.content_ar}
                onChange={(content) => setFieldValue("content_ar", content)}
                placeholder="Enter Content in Arabic"
              />
            </div>
            <FormInput
              name="slug"
              label="Slug"
              placeholder="Enter slug"
              required
            />
          </div>

          <FormRadioGroup
            name="status"
            label="Status"
            options={statusOptions}
            required
          />

          <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-6">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={isSubmitting}
            >
              {page ? "Update Pages" : "Create Pages"}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default PageEditUpdate;
