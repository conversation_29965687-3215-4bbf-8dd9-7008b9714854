import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import SearchInput from "@/components/ui/SearchInput";
import FilterDropdown from "@/components/ui/FilterDropdown";
import Button from "@/components/ui/Button";
import { FaFilter, FaTimes } from "react-icons/fa";
import { fetchData } from "@/hooks/useApi";

const ProductFilters = ({ onChange }) => {
  const { t } = useTranslation();
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    category: '',
    brand: '',
    price_range: '',
    stock_status: ''
  });

  // Fetch categories for filter dropdown
  const { data: categoriesData } = fetchData("admin/categories", { pagination: false });
  const categories = categoriesData?.data || [];

  // Fetch brands for filter dropdown
  const { data: brandsData } = fetchData("admin/brands", { pagination: false });
  const brands = brandsData?.data || [];

  const statusOptions = [
    { label: "All Status", value: "" },
    { label: "Active", value: "active" },
    { label: "Draft", value: "draft" },
    { label: "Inactive", value: "inactive" },
  ];

  const stockStatusOptions = [
    { label: "All Stock", value: "" },
    { label: "In Stock", value: "in_stock" },
    { label: "Low Stock", value: "low_stock" },
    { label: "Out of Stock", value: "out_of_stock" },
  ];

  const priceRangeOptions = [
    { label: "All Prices", value: "" },
    { label: "Under AED 50", value: "0-50" },
    { label: "AED 50 - 100", value: "50-100" },
    { label: "AED 100 - 500", value: "100-500" },
    { label: "AED 500 - 1000", value: "500-1000" },
    { label: "Over AED 1000", value: "1000+" },
  ];

  const categoryOptions = [
    { label: "All Categories", value: "" },
    ...categories.map(category => ({
      label: category.name_en || category.name,
      value: category.id
    }))
  ];

  const brandOptions = [
    { label: "All Brands", value: "" },
    ...brands.map(brand => ({
      label: brand.name_en || brand.name,
      value: brand.id
    }))
  ];

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onChange(newFilters);
  };

  const handleSearchChange = (value) => {
    handleFilterChange('search', value);
  };

  const clearFilters = () => {
    const clearedFilters = {
      search: '',
      status: '',
      category: '',
      brand: '',
      price_range: '',
      stock_status: ''
    };
    setFilters(clearedFilters);
    onChange(clearedFilters);
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 mb-6">
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search Input */}
        <div className="flex-1">
          <SearchInput
            placeholder={t("products.searchPlaceholder") || "Search products..."}
            value={filters.search}
            onChange={handleSearchChange}
            className="w-full"
          />
        </div>

        {/* Filter Dropdowns */}
        <div className="flex flex-wrap gap-3">
          <FilterDropdown
            label="Status"
            options={statusOptions}
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
            className="min-w-[120px]"
          />

          <FilterDropdown
            label="Category"
            options={categoryOptions}
            value={filters.category}
            onChange={(value) => handleFilterChange('category', value)}
            className="min-w-[140px]"
          />

          <FilterDropdown
            label="Brand"
            options={brandOptions}
            value={filters.brand}
            onChange={(value) => handleFilterChange('brand', value)}
            className="min-w-[120px]"
          />

          <FilterDropdown
            label="Price Range"
            options={priceRangeOptions}
            value={filters.price_range}
            onChange={(value) => handleFilterChange('price_range', value)}
            className="min-w-[140px]"
          />

          <FilterDropdown
            label="Stock Status"
            options={stockStatusOptions}
            value={filters.stock_status}
            onChange={(value) => handleFilterChange('stock_status', value)}
            className="min-w-[130px]"
          />

          {/* Clear Filters Button */}
          {hasActiveFilters && (
            <Button
              variant="outline"
              onClick={clearFilters}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
            >
              <FaTimes className="w-3 h-3" />
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-gray-600 font-medium">Active filters:</span>
            
            {filters.search && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Search: "{filters.search}"
                <button
                  onClick={() => handleFilterChange('search', '')}
                  className="ml-1 hover:text-blue-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}

            {filters.status && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                Status: {statusOptions.find(opt => opt.value === filters.status)?.label}
                <button
                  onClick={() => handleFilterChange('status', '')}
                  className="ml-1 hover:text-green-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}

            {filters.category && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                Category: {categoryOptions.find(opt => opt.value === filters.category)?.label}
                <button
                  onClick={() => handleFilterChange('category', '')}
                  className="ml-1 hover:text-purple-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}

            {filters.brand && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                Brand: {brandOptions.find(opt => opt.value === filters.brand)?.label}
                <button
                  onClick={() => handleFilterChange('brand', '')}
                  className="ml-1 hover:text-orange-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}

            {filters.price_range && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                Price: {priceRangeOptions.find(opt => opt.value === filters.price_range)?.label}
                <button
                  onClick={() => handleFilterChange('price_range', '')}
                  className="ml-1 hover:text-yellow-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}

            {filters.stock_status && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                Stock: {stockStatusOptions.find(opt => opt.value === filters.stock_status)?.label}
                <button
                  onClick={() => handleFilterChange('stock_status', '')}
                  className="ml-1 hover:text-red-900"
                >
                  <FaTimes className="w-2 h-2" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductFilters;
