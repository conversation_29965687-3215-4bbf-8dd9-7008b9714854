import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaTimes, FaBox, FaTag, FaCalendar, FaUser, FaGlobe } from "react-icons/fa";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";

const PreviewModal = ({ product, onClose }) => {
  const [activeTab, setActiveTab] = useState("details");

  if (!product) return null;

  const tabs = [
    { id: "details", label: "Product Details", icon: FaBox },
    { id: "pricing", label: "Pricing & Stock", icon: FaTag },
    { id: "media", label: "Media", icon: FaGlobe },
    { id: "seo", label: "SEO & Meta", icon: FaGlobe },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "details":
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product Name (English)
                </label>
                <p className="text-gray-900">{product.title_en || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product Name (Arabic)
                </label>
                <p className="text-gray-900" dir="rtl">{product.title_ar || 'N/A'}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  SKU
                </label>
                <p className="text-gray-900 font-mono">{product.sku || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Barcode
                </label>
                <p className="text-gray-900 font-mono">{product.barcode || 'N/A'}</p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description (English)
              </label>
              <div 
                className="text-gray-900 prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: product.description_en || 'No description available' }}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description (Arabic)
              </label>
              <div 
                className="text-gray-900 prose prose-sm max-w-none"
                dir="rtl"
                dangerouslySetInnerHTML={{ __html: product.description_ar || 'لا يوجد وصف متاح' }}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <p className="text-gray-900">{product.category?.name || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Brand
                </label>
                <p className="text-gray-900">{product.brand?.name || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  product.status === 'active' 
                    ? 'bg-green-100 text-green-800'
                    : product.status === 'draft'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {product.status}
                </span>
              </div>
            </div>
          </div>
        );

      case "pricing":
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Regular Price
                </label>
                <p className="text-gray-900 text-lg font-semibold">
                  AED {product.regular_price || '0.00'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Offer Price
                </label>
                <p className="text-gray-900 text-lg font-semibold text-green-600">
                  {product.offer_price ? `AED ${product.offer_price}` : 'No offer price'}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Stock Quantity
                </label>
                <p className="text-gray-900 text-lg font-semibold">
                  {product.stock_quantity || 0}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reserved Stock
                </label>
                <p className="text-gray-900">
                  {product.reserved_stock || 0}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Low Stock Threshold
                </label>
                <p className="text-gray-900">
                  {product.low_stock_threshold || 'Not set'}
                </p>
              </div>
            </div>

            {product.offer_price && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Offer Start Date
                  </label>
                  <p className="text-gray-900">
                    {product.offer_start_date ? new Date(product.offer_start_date).toLocaleDateString() : 'Not set'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Offer End Date
                  </label>
                  <p className="text-gray-900">
                    {product.offer_end_date ? new Date(product.offer_end_date).toLocaleDateString() : 'Not set'}
                  </p>
                </div>
              </div>
            )}
          </div>
        );

      case "media":
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Featured Image
              </label>
              {product.featured_image ? (
                <div className="w-48 h-48 rounded-lg overflow-hidden border border-gray-200">
                  <img
                    src={product.featured_image}
                    alt={product.title_en}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="w-48 h-48 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                  <div className="text-center">
                    <FaBox className="mx-auto h-12 w-12 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-500">No image available</p>
                  </div>
                </div>
              )}
            </div>

            {product.gallery && product.gallery.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gallery Images
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {product.gallery.map((image, index) => (
                    <div key={index} className="w-24 h-24 rounded-lg overflow-hidden border border-gray-200">
                      <img
                        src={image.url}
                        alt={`Gallery ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case "seo":
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Meta Title
              </label>
              <p className="text-gray-900">{product.meta_title || 'Not set'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Meta Description
              </label>
              <p className="text-gray-900">{product.meta_description || 'Not set'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Keywords
              </label>
              <p className="text-gray-900">{product.keywords || 'Not set'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                URL Slug
              </label>
              <p className="text-gray-900 font-mono">{product.slug || 'Not set'}</p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal isOpen={true} onClose={onClose} size="xl">
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">
          Product Preview
        </h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <FaTimes className="w-5 h-5" />
        </button>
      </div>

      <div className="flex border-b border-gray-200">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            {tab.label}
          </button>
        ))}
      </div>

      <div className="p-6 max-h-96 overflow-y-auto">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {renderTabContent()}
          </motion.div>
        </AnimatePresence>
      </div>

      <div className="flex justify-end p-6 border-t border-gray-200">
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </div>
    </Modal>
  );
};

export default PreviewModal;
