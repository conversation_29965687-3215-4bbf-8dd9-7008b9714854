import Button from "@/components/ui/Button";

const PreviewModal = ({ blog, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-5xl max-h-[90vh] flex flex-col">
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-xl font-semibold text-gray-900">
            Preview: {blog?.title_en}
          </h3>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 text-gray-500 hover:text-gray-900"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="flex-1 overflow-y-auto p-6 space-y-8">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <span className="h-3 w-3 rounded-full bg-blue-500"></span>
              <h4 className="text-lg font-medium text-gray-900">English Content</h4>
            </div>
            <div
              className="prose max-w-none p-4 bg-gray-50 rounded-lg"
              dangerouslySetInnerHTML={{ __html: blog?.content_en }}
            />
          </div>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <span className="h-3 w-3 rounded-full bg-green-500"></span>
              <h4 className="text-lg font-medium text-gray-900">Arabic Content</h4>
            </div>
            <div
              className="prose max-w-none p-4 bg-gray-50 rounded-lg"
              dangerouslySetInnerHTML={{ __html: blog?.content_ar }}
            />
          </div>
        </div>
        <div className="p-4 border-t border-gray-200 flex justify-end">
          <Button 
            variant="outline" 
            onClick={onClose}
            className="px-6"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PreviewModal;