import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import BlogFilters from "./blogFilters";
import PreviewModal from "./PreviewModal";
import { FaEdit, FaTrash, FaClipboardList, FaEye, FaPlus } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";

const blogIndex = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { deleteMutation } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedBlog, setSelectedBlog] = useState(null);
  const [previewBlog, setPreviewBlog] = useState(null);

  const [filterOptions, setFilterOptions] = useState({
    search: '',
    status: true
  })

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  const {
    data: blogsData,
    isLoading,
    isError: blogsError,
    refetch,
  } = fetchData("admin/blogs", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    ...filterOptions
  });

  const blogList = blogsData?.data?.data || [];
  const paginationInfo = {
    currentPage: blogsData?.data?.current_page || 1,
    perPage: blogsData?.data?.per_page || itemsPerPage,
    totalItems: blogsData?.data?.total_items || 0,
    totalPages: blogsData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters)
    setCurrentPage(1)
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handleEditBlog = (blog) => {
    navigate(`/blog/posts/edit/${blog.id}`);
  };

  const handleDeleteClick = (blog) => {
    setSelectedBlog(blog);
  };

  const handlePreviewClick = (blog) => {
    setPreviewBlog(blog);
  };

  const handleDeleteBlog = async () => {
    if (!selectedBlog) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/blogs/${selectedBlog.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setSelectedBlog(null);
          refetch();
          toast.success("Blog deleted successfully.");
        },
        onError: (error) => {
          console.error(
            "Blog deletion failed:",
            error?.response?.data || error.message
          );
          toast.error(
            "Blog deletion failed: " +
              (error?.response?.data?.message || error.message)
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const canEditBlog = (blog) => true

  const canDeleteBlog = (blog) => true

  // Table columns configuration
  const columns = [
    {
      header: "ID",
      accessor: "id",
    },
    {
      header: "Title (EN)",
      accessor: "title_en",
    },
    {
      header: "Title (AR)",
      accessor: "title_ar",
    },
    {
      header: "Blog Category",
      accessor: "blog_category_id",
    },
    {
      header: "Meta Title",
      accessor: "meta_title",
    },
    {
      header: "Slug",
      accessor: "slug",
    },
    {
      header: "Status",
      accessor: "status",
      render: (row) => {
        const statusColors = {
          published: "bg-green-100 text-green-800",
          draft: "bg-red-100 text-red-600",
        };

        return (
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
              statusColors[row.status]
            }`}
          >
            {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
          </span>
        );
      },
    },
    {
      header: "Actions",
      render: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditBlog(row)}
            disabled={!canEditBlog(row)}
            title={
              !canEditBlog(row)
                ? "You don't have permission to edit this blog"
                : "Edit blog"
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePreviewClick(row)}
            title="Preview blog content"
          >
            <FaEye className="text-blue-600" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteBlog(row)}
            title={
              !canDeleteBlog(row) ? "You can't delete this blog" : "Delete blog"
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  // Filter options
  return (
    <div className="max-w-7xl mx-auto relative pt-6">
      {isLoading  && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title="Blog List"
          icon={<FaClipboardList className="text-indigo-600" />}
          action={
            <Button
              variant="primary"
              className="ml-2"
              onClick={() => navigate("/blog/posts/add")}
            >
              <FaPlus className="mr-2" /> Add Blog
            </Button>
          }
        >
          <BlogFilters
            onChange={handleFilterChange}
          />

          <Table
            columns={columns}
            data={blogList}
            emptyMessage={t("blogCategory.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      {selectedBlog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">Confirm Delete</h3>
            <p className="text-gray-700 mb-6">
              Are you sure you want to delete the Blog{" "}
              <span className="font-semibold">{selectedBlog?.title_en}</span>?
              This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setSelectedBlog(null)}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteBlog}
                disabled={deleteLoading}
              >
                {deleteLoading ? <LoadingSpinner size={20} /> : "Delete"}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {previewBlog && (
        <PreviewModal
          blog={previewBlog}
          onClose={() => setPreviewBlog(null)}
        />
      )}
    </div>
  );
};

export default blogIndex;
