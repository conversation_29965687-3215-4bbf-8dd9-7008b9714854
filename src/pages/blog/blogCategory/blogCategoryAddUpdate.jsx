import { Formik, Form } from "formik";
import * as Yup from "yup";
import { FormInput, FormRadioGroup } from "@/components/ui/form";
import Button from "@/components/ui/Button";

const BlogCategoryAddUpdate = ({ blogCategory, onSubmit, onCancel }) => {
  const initialValues = blogCategory
    ? {
        id: blogCategory.id,
        title_en: blogCategory.title_en || "",
        title_ar: blogCategory.title_ar || "",
        slug: blogCategory.slug || "",
        status: blogCategory.status || "active",
      }
    : {
        title_en: "",
        title_ar: "",
        slug: "",
        status: "active",
      };

  const validationSchema = Yup.object({
    title_en: Yup.string().required("English Title is required"),
    slug: Yup.string().required("Slug is required"),
    status: Yup.string().required("Status is required"),
  });

  const statusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
  ];

  const handleSubmit = (values, { setSubmitting }) => {
    onSubmit(values);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting }) => (
        <Form className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              name="title_en"
              label="Title in English"
              placeholder="Enter title in English"
              required
            />
            <FormInput
              name="title_ar"
              label="Title in Arabic"
              placeholder="Enter title in Arabic"
            />
            <FormInput
              name="slug"
              label="Slug"
              placeholder="Enter slug"
              required
            />
          </div>

          <FormRadioGroup
            name="status"
            label="Status"
            options={statusOptions}
            required
          />

          <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {blogCategory ? "Update Category" : "Create Category"}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default BlogCategoryAddUpdate;
