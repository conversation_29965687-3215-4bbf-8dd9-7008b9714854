import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { FaEdit, FaTrash, FaKey, FaBlog, FaPlus, FaEye } from "react-icons/fa";
import Card from "@/components/ui/Card";
import Table from "@/components/ui/Table";
import PaginationInfo from "@/components/ui/PaginationInfo";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import BlogCategoryFilters from "./blogCategoryFilters";
import BlogCategoryForm from "./blogCategoryAddUpdate";
import { fetchData, useApi } from "@/hooks/useApi";
import usePagination from "@/hooks/usePagination";
import { toast } from "sonner";

const blogCategoryIndex = () => {
  const { postMutation, putMutation, deleteMutation } = useApi();

  const { t } = useTranslation();

  const {
    page: currentPage,
    pageSize: itemsPerPage,
    setPage: setCurrentPage,
    setPageSize: setItemsPerPage,
    resetPagination,
  } = usePagination(1, 10);

  // State for filters
  const [filterOptions, setFilterOptions] = useState({
    search: '',
    status: true
  });

  // State for modals
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedBlogCategory, setSelectedBlogCategory] = useState(null);
  const [editLoading, setEditLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const {
    data: blogCategoryData,
    isLoading,
    isError,
    refetch,
  } = fetchData("admin/blog-categories", {
    pagination: true,
    per_page: itemsPerPage,
    page: currentPage,
    ...filterOptions
  });

  const blogCategoryList = blogCategoryData?.data?.data || [];
  const paginationInfo = {
    currentPage: blogCategoryData?.data?.current_page || 1,
    perPage: blogCategoryData?.data?.per_page || itemsPerPage,
    totalItems: blogCategoryData?.data?.total_items || 0,
    totalPages: blogCategoryData?.data?.total_pages || 1,
  };

  const handleFilterChange = (filters) => {
    setFilterOptions(filters)
    setCurrentPage(1)
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  // Open edit modal
  const handleEditBlogCategory = (blogCategory) => {
    setSelectedBlogCategory(blogCategory);
    setEditModalOpen(true);
  };

  // Open delete modal
  const handleDeleteClick = (blogCategory) => {
    setSelectedBlogCategory(blogCategory);
    setDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    if (!selectedBlogCategory) return;
    setDeleteLoading(true);
    deleteMutation.mutate(
      {
        endpoint: `admin/blog-categories/${selectedBlogCategory.id}`,
        data: {},
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false);
          setSelectedBlogCategory(null);
          refetch();
          toast.success("B log category deleted successfully.");
        },
        onError: (error) => {
          console.error(
            "Category deletion failed:",
            error?.response?.data || error.message
          );
        },
        onSettled: () => {
          setDeleteLoading(false);
        },
      }
    );
  };

  const handleUpdateBlogCategory = (updatedBlogCategory) => {
    setEditLoading(true);
    const dataToSend = { ...updatedBlogCategory };
    delete dataToSend.confirmPassword;

    if (updatedBlogCategory.id && !dataToSend.password) {
      delete dataToSend.password;
    }

    const onSuccess = () => {
      setEditModalOpen(false);
      setSelectedBlogCategory(null);
      refetch();
      toast.success(`Category ${updatedBlogCategory.id ? "updated" : "created"} successfully.`);
    };
    const onError = (error) => {
      console.error(
        "Category update failed:",
        error?.response?.data || error.message
      );
    };
    const onSettled = () => setEditLoading(false);

    if (!updatedBlogCategory.id) {
      postMutation.mutate(
        { endpoint: "admin/blog-categories", data: dataToSend },
        { onSuccess, onError, onSettled }
      );
    } else {
      putMutation.mutate(
        {
          endpoint: `admin/blog-categories/${updatedBlogCategory.id}`,
          data: dataToSend,
        },
        { onSuccess, onError, onSettled }
      );
    }
  };

  const canEditBlogCategory = (blogCategory) => true

  const canDeleteBlogCategory = (blogCategory) => true

  // Table columns configuration
  const columns = [
    {
      header: "ID",
      accessor: "id",
    },
    {
      header: "Title (EN)",
      accessor: "title_en",
    },
    {
      header: "Title (AR)",
      accessor: "title_ar",
    },
    {
      header: "slug",
      accessor: "slug",
    },
    {
      header: "Status",
      accessor: "status",
      render: (row) => {
        const statusColors = {
          active: "bg-green-100 text-green-800",
          inactive: "bg-red-100 text-red-600",
        };

        return (
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
              statusColors[row.status]
            }`}
          >
            {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
          </span>
        );
      },
    },
    {
      header: "Actions",
      render: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditBlogCategory(row)}
            disabled={!canEditBlogCategory(row)}
            title={
              !canEditBlogCategory(row)
                ? "You don't have permission to edit this blog category"
                : "Edit blog category"
            }
          >
            <FaEdit className="text-indigo-600" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(row)}
            disabled={!canDeleteBlogCategory(row)}
            title={
              !canDeleteBlogCategory(row) ? "You can't delete this blog category" : "Delete blog category"
            }
          >
            <FaTrash className="text-red-600" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="max-w-7xl mx-auto relative">
      {isLoading && <LoadingSpinner size={64} overlay />}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title="Blog Categories List"
          icon={<FaBlog className="text-indigo-600" />}
          action={
            <Button
              variant="primary"
              onClick={() => {
                setSelectedBlogCategory(null);
                setEditModalOpen(true);
              }}
            >
              <FaPlus className="mr-2" /> Add Blog Category
            </Button>
          }
        >
          <BlogCategoryFilters
            onChange={handleFilterChange}
          />

          <Table
            columns={columns}
            data={blogCategoryList}
            emptyMessage={t("blogCategory.emptyMessage")}
          />

          <PaginationInfo
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            currentPage={paginationInfo.currentPage}
            totalItems={paginationInfo.totalItems}
            totalPages={paginationInfo.totalPages}
            onPageChange={setCurrentPage}
            className="mt-4"
          />
        </Card>
      </motion.div>

      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title={selectedBlogCategory ? "Edit Blog Category" : "Add New Blog Category"}
        size="xl"
      >
        <BlogCategoryForm
          blogCategory={selectedBlogCategory}
          onSubmit={handleUpdateBlogCategory}
          onCancel={() => setEditModalOpen(false)}
          loading={editLoading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Confirm Delete"
        size="sm"
      >
        <div className="py-4">
          <p className="text-gray-700">
            Are you sure you want to delete the Blog Category{" "}
            <span className="font-semibold">{selectedBlogCategory?.title_en}</span>?
            This action cannot be undone.
          </p>
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDelete}
            disabled={deleteLoading}
          >
            {deleteLoading ? <LoadingSpinner size={20} /> : "Delete"}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default blogCategoryIndex;
