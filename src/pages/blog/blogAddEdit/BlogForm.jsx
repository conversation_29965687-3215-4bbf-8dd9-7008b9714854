import { useState, useEffect, useCallback } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { generateSlug } from "@/utils/slugUtils";
import {
  FormInput,
  FormRadioGroup,
  FormSelect,
  FormTextarea,
  RichTextEditor,
} from "@/components/ui/form";
import ImageUpload from "@/components/ui/ImageUpload";
import Button from "@/components/ui/Button";
import { fetchData } from "@/hooks/useApi";
import { toast } from "sonner";

const blogAddUpdate = ({ blog, onSubmit, onCancel }) => {
  const [blogCategoryOptions, setBlogCategoryOptions] = useState([]);

  const { data: response, isLoading: categoryLoading, isError, refetch } = fetchData("admin/blog-categories");

  useEffect(() => {
      if (response) {
          const categories = response?.data?.data || [];
          const formatted = [
              { label: "Select The blog category", value: "" },
              ...categories.map((cat) => ({
                  label: cat.title_en,
                  value: String(cat.id),
              })),
          ];
          setBlogCategoryOptions(formatted);
      }
      if (isError) {
          return toast.error("Could not fetch blog categories");
      }
  }, [response, isError]);

  const initialValues = blog
    ? {
        id: blog.id,
        title_en: blog.title_en || "",
        title_ar: blog.title_ar || "",
        blog_category_id: blog.blog_category_id || "",
        slug: blog.slug || "",
        content_en: blog.content_en || "",
        content_ar: blog.content_ar || "",
        summary_en: blog.summary_en || "",
        summary_ar: blog.summary_ar || "",
        meta_title: blog.meta_title || "",
        keywords: blog.keywords || "",
        meta_description: blog.meta_description || "",
        featured_image: blog.featured_image_url || "",
        status: blog.status || "draft",
      }
    : {
        title_en: "",
        title_ar: "",
        blog_category_id: "",
        slug: "",
        content_en: "",
        content_ar: "",
        summary_en: "",
        summary_ar: "",
        meta_title: "",
        keywords: "",
        meta_description: "",
        featured_image: "",
        status: "draft",
      };

  const validationSchema = Yup.object({
    blog_category_id: Yup.string().required("Blog Category is required"),
    title_en: Yup.string().required("English Title is required"),
    slug: Yup.string().required("Slug is required"),
    status: Yup.string().required("Status is required"),
  });

  const statusOptions = [
    { label: "Draft", value: "draft" },
    { label: "Published", value: "published" },
  ];

  const handleTitleChange = useCallback((e, setFieldValue) => {
    const newTitle = e.target.value;

    const timer = setTimeout(() => {
      const newSlug = generateSlug(newTitle);
      setFieldValue('slug', newSlug)
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = (values, { setSubmitting }) => {
    const formattedValues = {
      ...values,
    };
    onSubmit(formattedValues);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue, values }) => (
        <Form className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-[1fr_30%] gap-8 max-w-full">
            <div className="space-y-4">
              <FormInput
                name="title_en"
                label="Title in English"
                placeholder="Enter Title in English"
                required
                onChange={(e) => {
                  setFieldValue('title_en', e.target.value);
                  handleTitleChange(e, setFieldValue);
                }}
              />
              <FormInput
                name="title_ar"
                label="Title in Arabic"
                placeholder="Enter Title in Arabic"
              />
              <FormInput
                name="slug"
                label="Slug"
                placeholder="Enter slug"
                required
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 my-2">
                  Content in English
                </label>
                <RichTextEditor
                  name="content_en"
                  value={values.content_en}
                  onChange={(content) => setFieldValue("content_en", content)}
                  placeholder="Enter Content in English"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 my-2">
                  Content in Arabic
                </label>
                <RichTextEditor
                  name="content_ar"
                  value={values.content_ar}
                  onChange={(content) => setFieldValue("content_ar", content)}
                  placeholder="Enter Content in Arabic"
                />
              </div>
            </div>

            <div className="space-y-4">
              <FormSelect
                name="blog_category_id"
                label="Blog Category"
                options={blogCategoryOptions}
                required
                loading={categoryLoading}
              />
              <FormTextarea
                name="summary_en"
                label="Summary in English"
                placeholder="Enter Summary in English"
              />
              <FormTextarea
                name="summary_ar"
                label="Summary in Arabic"
                placeholder="Enter Summary in Arabic"
              />
              <FormInput
                name="meta_title"
                label="Title in Meta"
                placeholder="Enter Title in Meta"
              />
              <FormInput
                name="keywords"
                label="Keywords"
                placeholder="Enter keywords"
              />
              <FormTextarea
                name="meta_description"
                label="Meta Description"
                placeholder="Enter Meta Description"
              />
              <div className="mb">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Featured Image
                </label>
                <ImageUpload
                  value={initialValues.featured_image}
                  onUploadSuccess={(url) => setFieldValue("featured_image", url)}
                />
              </div>
              <FormRadioGroup
                name="status"
                label="Status"
                options={statusOptions}
                required
              />
            </div>
          </div>

          <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-8">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {blog ? "Update Blog" : "Create Blog"}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default blogAddUpdate;
