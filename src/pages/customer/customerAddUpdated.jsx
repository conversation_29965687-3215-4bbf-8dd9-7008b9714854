// import { useState, useRef } from "react";
// import { Formik, Form } from "formik";
// import * as Yup from "yup";
// import { uploadSingleFile } from "@/utils/fileUploadHelpers";
// import LoadingSpinner from "../../components/ui/LoadingSpinner";
// import {
//   FormInput,
//   FormRadioGroup,
//   FormSelect,
// } from "../../components/ui/form";
// import Button from "../../components/ui/Button";
// import ImageUpload from "../../components/ui/ImageUpload";

// const customerAddUpdated = ({ user, onSubmit, onCancel }) => {
//   const isNew = !user;

//   const initialValues = user
//     ? {
//         name: user.name || "",
//         email: user.email || "",
//         password: "",
//         password_confirmation: "",
//         phone: user.phone || "",
//         avatar: user.avatar || "",
//         status: user.status || "pending",
//         is_active: user.is_active || "0",
//         gender: user.gender || "male",
//         date_of_birth: user.date_of_birth || "",
//         loyalty_points: user.loyalty_points || 0,
//         customer_type: user.customer_type || "retail",
//         preferred_language: user.preferred_language || "en",
//         preferred_currency: user.preferred_currency || "AED",
//       }
//     : {
//         name: "",
//         email: "",
//         password: "",
//         password_confirmation: "",
//         phone: "",
//         avatar: "",
//         status: "pending",
//         is_active: "0",
//         gender: "male",
//         date_of_birth: "",
//         loyalty_points: 0,
//         customer_type: "retail",
//         preferred_language: "en",
//         preferred_currency: "AED",
//       };

//   const validationSchema = Yup.object({
//     name: Yup.string().required("Name is required"),
//     email: Yup.string().email("Invalid email").required("Email is required"),
//     password: isNew
//       ? Yup.string()
//           .required("Password is required")
//           .min(6, "Password must be at least 6 characters")
//       : Yup.string(),
//     password_confirmation: Yup.string().when("password", {
//       is: (val) => val && val.length > 0,
//       then: Yup.string().oneOf([Yup.ref("password")], "Passwords must match"),
//     }),
//     phone: Yup.string().required("Phone is required"),
//     status: Yup.string().required("Status is required"),
//     gender: Yup.string().required("Gender is required"),
//     date_of_birth: Yup.string().required("Date of Birth is required"),
//     loyalty_points: Yup.number().min(0, "Loyalty Points must be at least 0"),
//   });

//   const statusOptions = [
//     { label: "Pending", value: "pending" },
//     { label: "Active", value: "active" },
//     { label: "Inactive", value: "inactive" },
//   ];

//   const genderOptions = [
//     { label: "Male", value: "male" },
//     { label: "Female", value: "female" },
//   ];

//   const customerTypeOptions = [
//     { label: "Retail", value: "retail" },
//     { label: "Wholesale", value: "wholesale" },
//   ];

//   const handleSubmit = (values, { setSubmitting }) => {
//     const formData = new FormData();
//     Object.entries(values).forEach(([key, value]) => {
//       formData.append(key, value ?? "");
//     });
//     onSubmit(formData);
//     setSubmitting(false);
//   };

//   return (
//     <Formik
//       initialValues={initialValues}
//       validationSchema={validationSchema}
//       onSubmit={handleSubmit}
//       enableReinitialize
//     >
//       {({ isSubmitting, setFieldValue }) => (
//         <Form className="space-y-6">
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//             <FormInput
//               name="name"
//               label="Name"
//               placeholder="Enter full name"
//               required
//             />
//             <FormInput
//               name="email"
//               label="Email"
//               placeholder="Enter email address"
//               required
//             />
//             <FormInput
//               name="password"
//               label="Password"
//               type="password"
//               placeholder="Enter password"
//             />
//             <FormInput
//               name="password_confirmation"
//               label="Confirm Password"
//               type="password"
//               placeholder="Confirm password"
//             />
//             <FormInput
//               name="phone"
//               label="Phone"
//               placeholder="Enter phone number"
//               required
//             />
//             <div>
//               <label className="block text-sm font-medium text-gray-700">
//                 Avatar
//               </label>
//               <ImageUpload
//                 onUploadSuccess={(url) => setFieldValue("avatar", url)}
//               />
//             </div>
//             <FormInput
//               name="date_of_birth"
//               label="Date of Birth"
//               type="date"
//               required
//             />
//             <FormInput
//               name="loyalty_points"
//               label="Loyalty Points"
//               type="number"
//             />
//             <FormSelect
//               name="gender"
//               label="Gender"
//               options={genderOptions}
//               required
//             />
//             <FormSelect
//               name="customer_type"
//               label="Customer Type"
//               options={customerTypeOptions}
//             />
//             <FormInput
//               name="preferred_language"
//               label="Preferred Language"
//               placeholder="e.g., en"
//             />
//             <FormInput
//               name="preferred_currency"
//               label="Preferred Currency"
//               placeholder="e.g., AED"
//             />
//           </div>

//           <FormRadioGroup
//             name="status"
//             label="Status"
//             options={statusOptions}
//             required
//           />

//           <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-6">
//             <Button type="button" variant="outline" onClick={onCancel}>
//               Cancel
//             </Button>
//             <Button
//               type="submit"
//               variant="primary"
//               loading={isSubmitting}
//               disabled={isSubmitting}
//             >
//               {user ? "Update Customer" : "Create Customer"}
//             </Button>
//           </div>
//         </Form>
//       )}
//     </Formik>
//   );
// };

// export default customerAddUpdated;

import { useState, useRef } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { uploadSingleFile } from "@/utils/fileUploadHelpers";
import LoadingSpinner from "../../components/ui/LoadingSpinner";
import {
  FormInput,
  FormRadioGroup,
  FormSelect,
} from "../../components/ui/form";
import Button from "../../components/ui/Button";
import ImageUpload from "../../components/ui/ImageUpload";
import FormTextarea from "../../components/ui/form/FormTextarea";

const customerAddUpdated = ({ user, onSubmit, onCancel }) => {
  const isNew = !user;

  const initialValues = user
    ? {
        name: user.name || "",
        email: user.email || "",
        password: "",
        password_confirmation: "",
        phone: user.phone || "",
        avatar: user.avatar || "",
        status: user.status || "pending",
        is_active: user.is_active || "0",
        gender: user.gender || "male",
        date_of_birth: user.date_of_birth || "",
        loyalty_points: user.loyalty_points || 0,
        customer_type: user.customer_type || "retail",
        preferred_language: user.preferred_language || "en",
        preferred_currency: user.preferred_currency || "AED",
      }
    : {
        name: "",
        email: "",
        password: "",
        password_confirmation: "",
        phone: "",
        avatar: "",
        status: "pending",
        is_active: "0",
        gender: "male",
        date_of_birth: "",
        loyalty_points: 0,
        customer_type: "retail",
        preferred_language: "en",
        preferred_currency: "AED",
      };

  const validationSchema = Yup.object({
    name: Yup.string().required("Name is required"),
    email: Yup.string().email("Invalid email").required("Email is required"),
    password: isNew
      ? Yup.string()
          .required("Password is required")
          .min(6, "Password must be at least 6 characters")
      : Yup.string(),
    password_confirmation: Yup.string().when("password", {
      is: (val) => val && val.length > 0,
      then: Yup.string().oneOf([Yup.ref("password")], "Passwords must match"),
    }),
    phone: Yup.string().required("Phone is required"),
    status: Yup.string().required("Status is required"),
    gender: Yup.string().required("Gender is required"),
    date_of_birth: Yup.string().required("Date of Birth is required"),
    loyalty_points: Yup.number().min(0, "Loyalty Points must be at least 0"),
  });

  const statusOptions = [
    { label: "Pending", value: "pending" },
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
  ];

  const genderOptions = [
    { label: "Male", value: "male" },
    { label: "Female", value: "female" },
  ];

  const customerTypeOptions = [
    { label: "Retail", value: "retail" },
    { label: "Wholesale", value: "wholesale" },
  ];

  const handleSubmit = (values, { setSubmitting }) => {
    const formData = new FormData();
    Object.entries(values).forEach(([key, value]) => {
      formData.append(key, value ?? "");
    });
    onSubmit(formData);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, setFieldValue }) => (
        <Form className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormInput
              name="name"
              label="Name"
              placeholder="Enter full name"
              required
            />
            <FormInput
              name="email"
              label="Email"
              placeholder="Enter email address"
              required
            />
            <FormInput
              name="password"
              label="Password"
              type="password"
              placeholder="Enter password"
            />
            <FormInput
              name="password_confirmation"
              label="Confirm Password"
              type="password"
              placeholder="Confirm password"
            />
            <FormTextarea
              name="phone"
              label="Phone"
              placeholder="Enter phone number"
              required
            />
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Avatar
              </label>
              <ImageUpload
                onUploadSuccess={(url) => setFieldValue("avatar", url)}
              />
            </div>
            <FormInput
              name="date_of_birth"
              label="Date of Birth"
              type="date"
              required
            />
            <FormInput
              name="loyalty_points"
              label="Loyalty Points"
              type="number"
            />
            <FormSelect
              name="gender"
              label="Gender"
              options={genderOptions}
              required
            />
            <FormSelect
              name="customer_type"
              label="Customer Type"
              options={customerTypeOptions}
            />
            <FormInput
              name="preferred_language"
              label="Preferred Language"
              placeholder="e.g., en"
            />
            <FormInput
              name="preferred_currency"
              label="Preferred Currency"
              placeholder="e.g., AED"
            />
          </div>

          <FormRadioGroup
            name="status"
            label="Status"
            options={statusOptions}
            required
          />

          <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-6">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {user ? "Update Customer" : "Create Customer"}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default customerAddUpdated;
