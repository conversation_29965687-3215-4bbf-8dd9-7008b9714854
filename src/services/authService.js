import api from '@/config/axios';
import {
  loginStart,
  loginSuccess,
  loginFailure,
  registerStart,
  registerSuccess,
  registerFailure
} from '../store/slices/authSlice';
import { setCookie, removeCookie } from '../utils/cookies';

// Auth service functions
export const login = (credentials) => async (dispatch) => {
  dispatch(loginStart());
  try {
    const response = await api.post('/login', credentials);
    const data = response.data.data;
    if (data.user) {
      // Store user data and token
      const authData = {
        user: data.user,
        token: data.token,
        role: data.role,
        permissions: data.permissions
      };
      // Set token in cookie
      setCookie('auth_token', data.token.access_token, 1);
      setCookie('refresh_token', data.token.refresh_token, 1);
      setCookie('user', JSON.stringify(data.user), 1);
      setCookie('role', data.role, 1);

      // Store user permissions in localStorage
      localStorage.setItem('user_permissions', JSON.stringify(data.permissions));

      // Fetch all available permissions and store in localStorage
      try {
        const allPermRes = await api.get('/admin/permissions?pagination=false');
        const allPermissions = allPermRes.data?.data || allPermRes.data;
        // Store only the 'name' property of each permission
        const permissionNames = Array.isArray(allPermissions)
          ? allPermissions.map((perm) => perm.name)
          : [];
        localStorage.setItem('all_permissions', JSON.stringify(permissionNames));
      } catch (permError) {
        // Optionally handle error, but don't block login
        console.error('Failed to fetch all permissions:', permError);
      }

      dispatch(loginSuccess(authData));
      return authData;
    } else {
      throw new Error(data.message || 'Login failed');
    }
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message || 'Login failed';
    dispatch(loginFailure(errorMessage));
    throw new Error(errorMessage);
  }
};

export const register = (userData) => async (dispatch) => {
  dispatch(registerStart());
  try {
    // For demo purposes, check if we're in demo mode
    if (process.env.NODE_ENV === 'development' && userData.mockUser) {
      // This is for testing different roles
      const mockToken = 'mock-token-' + Date.now();

      // Create auth data with the mock user
      const authData = {
        user: userData.mockUser,
        token: mockToken
      };

      // Set token in cookie with 7 days expiry
      setCookie('auth_token', mockToken, 7);

      dispatch(registerSuccess(authData));
      return authData;
    } else {
      // Normal API registration flow
      const response = await api.post('/register', userData);
      const data = response.data;

      if (data.success) {
        // Store user data and token
        const authData = {
          user: data.user,
          token: data.token
        };

        // Set token in cookie with 7 days expiry
        setCookie('auth_token', data.token, 7);

        dispatch(registerSuccess(authData));
        return authData;
      } else {
        throw new Error(data.message || 'Registration failed');
      }
    }
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message || 'Registration failed';
    dispatch(registerFailure(errorMessage));
    throw new Error(errorMessage);
  }
};

export const logoutUser = () => {
  // Remove token from cookie
  removeCookie('auth_token');
  removeCookie('refresh_token');
  removeCookie('user');
  removeCookie('role');
  removeCookie('permissions');
};
