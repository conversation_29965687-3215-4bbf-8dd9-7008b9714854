import api from '@/config/axios';
import { toast } from 'sonner';

/**
 * Common service for making API requests
 * @param {string} endpoint - API endpoint (without base URL)
 * @param {string} method - HTTP method (GET, POST, PUT, etc.)
 * @param {object} [data] - Request payload for POST/PUT requests
 * @param {object} [params] - Query parameters for GET requests
 * @param {boolean} [isMultipart=false] - Whether to send as multipart/form-data
 * @returns {Promise} Promise that resolves with response data or rejects with error
 */
const apiRequest = async (endpoint, method = "GET", data = null, params = {}, isMultipart = false) => {
    const config = {
        method,
        url: endpoint,
        data,
        params,
        ...(isMultipart && {
            headers: {
                'Content-Type': 'multipart/form-data',
                '_method': 'PUT'
            }
        })
    };

    return new Promise((resolve, reject) => {
        api(config)
          .then((response) => resolve(response))
          .catch((error) => {
              toast.error(
                error?.response?.data?.message ||
                error?.message ||
                'An unexpected error occurred'
              );
              reject(error);
          });
    });
};

// HTTP method specific functions
const get = (endpoint, params = {}) => apiRequest(endpoint, 'GET', null, params);
const post = (endpoint, data, isMultipart = false) => apiRequest(endpoint, 'POST', data, {}, isMultipart);
const postForm = (endpoint, data, isMultipart = true) => apiRequest(endpoint, 'POST', data, {}, isMultipart);
const putForm = (endpoint, data, isMultipart = true) => apiRequest(endpoint, 'POST', data, {}, isMultipart);
const put = (endpoint, data) => apiRequest(endpoint, 'PUT', data);
const del = (endpoint) => apiRequest(endpoint, 'DELETE');

export default apiRequest;
export { get, post, postForm, put, putForm, del as delete };